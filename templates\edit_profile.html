<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Profile | Librainian</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Cropper.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/cropperjs/dist/cropper.min.css">

    <style>
        /* Base Styles */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
        }

        /* Page Background */
        .profile-page-bg {
            background: linear-gradient(135deg, #e0f2f1 0%, #cee3e0 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        /* Card Styling */
        .profile-card {
            border-radius: 1rem;
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .profile-card-header {
            background: linear-gradient(135deg, #28345a 0%, #1a2542 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border-bottom: none;
        }

        /* Form Styling */
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .form-control {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            border: 1px solid #ced4da;
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .form-control:focus {
            border-color: #28345a;
            box-shadow: 0 0 0 0.25rem rgba(40, 52, 90, 0.25);
        }

        /* Button Styling */
        .btn-primary {
            background-color: #28345a;
            border-color: #28345a;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .btn-primary:hover {
            background-color: #1a2542;
            border-color: #1a2542;
            transform: translateY(-2px);
        }

        .btn-outline-secondary {
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }

        /* Breadcrumb Styling */
        .breadcrumb {
            background-color: transparent;
            padding: 0.5rem 0;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item a {
            color: #28345a;
            text-decoration: none;
            transition: color 0.2s ease-in-out;
        }

        .breadcrumb-item a:hover {
            color: #1a2542;
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: #6c757d;
        }

        /* Alert Styling */
        .alert {
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
        }

        /* Image Upload Styling */
        .image-upload-container {
            position: relative;
            width: 100%;
            margin-bottom: 1rem;
        }

        .image-preview {
            width: 100%;
            height: 150px;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            border: 2px dashed #ced4da;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            transition: all 0.2s ease-in-out;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .image-preview:hover {
            border-color: #28345a;
            cursor: pointer;
        }

        .upload-icon {
            font-size: 2rem;
            color: #6c757d;
        }

        /* Modal Styling */
        .modal-content {
            border-radius: 1rem;
            border: none;
        }

        .modal-header {
            background-color: #28345a;
            color: white;
            border-bottom: none;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            border-top: none;
            padding: 1rem 1.5rem 1.5rem;
        }
    </style>
</head>
<body>

<div class="profile-page-bg">
    <div class="container">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/{{role}}/dashboard/"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="breadcrumb-item"><a href="/{{role}}/profile/">Profile</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Profile</li>
            </ol>
        </nav>

        <!-- Alert Messages -->
        {% if messages %}
        <div id="messageContainer">
            {% for message in messages %}
            {% if message.tags == 'success' %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'error' %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'warning' %}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'info' %}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endif %}
            {% endfor %}
        </div>
        {% endif %}

        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="profile-card shadow">
                    <div class="profile-card-header">
                        <h4 class="mb-0"><i class="fas fa-user-edit me-2"></i>Edit {{ role|title }} Profile</h4>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" action="/{{role}}/edit-profile/" enctype="multipart/form-data" class="needs-validation" novalidate>
                            {% csrf_token %}

                            <!-- Profile Image Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-image me-2"></i>Profile Image</h5>
                                <div class="image-upload-container">
                                    <div class="image-preview" id="imagePreviewContainer" onclick="document.getElementById('image').click()">
                                        <div class="text-center" id="uploadPlaceholder">
                                            <div class="upload-icon mb-2">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <p class="mb-0">Click to upload image</p>
                                            <small class="text-muted">Image should be 1:6 in ratio and within 100KB</small>
                                        </div>
                                        {% if profile.image %}
                                        <img id="currentImagePreview" src="{{ profile.image.url }}" alt="Current profile image" style="display: none;">
                                        {% else %}
                                        <img id="currentImagePreview" src="" alt="Current profile image" style="display: none;">
                                        {% endif %}
                                    </div>
                                    <input type="file" id="image" name="image" class="form-control visually-hidden" accept="image/*">
                                </div>
                            </div>

                            <!-- Personal Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-user me-2"></i>Personal Information</h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="first_name" name="first_name" class="form-control" value="{{ user.first_name }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your first name.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="last_name" name="last_name" class="form-control" value="{{ user.last_name }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your last name.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" id="email" name="email" class="form-control" value="{{ user.email }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid email address.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Role-specific Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-id-badge me-2"></i>{{ role|title }} Information
                                </h5>

                                {% if role == "manager" %}
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" value="{{ profile.manager_phone_num }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" value="{{ profile.manager_address }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your address.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% elif role == "librarian" %}
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="libraryname" class="form-label">Library Name <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                                            <input type="text" id="libraryname" name="libraryname" class="form-control" value="{{ profile.library_name }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your library name.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" value="{{ profile.librarian_phone_num }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" value="{{ profile.librarian_address }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your address.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="google_map" class="form-label">Google Map Link <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map"></i></span>
                                            <input type="text" id="google_map" name="google_map" class="form-control" value="{{ profile.google_map_url }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid Google Map link.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="discount" class="form-label">Discount (%)</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-percent"></i></span>
                                            <input type="number" id="discount" name="discount" class="form-control" value="{{ profile.discount_amount }}" min="0" max="100">
                                            <div class="invalid-feedback">
                                                Please enter a valid discount percentage (0-100).
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-info-circle"></i></span>
                                            <textarea id="description" name="description" class="form-control" rows="2" required>{{ profile.description }}</textarea>
                                            <div class="invalid-feedback">
                                                Please provide a description.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% elif role == "sublibrarian" %}
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" value="{{ profile.sublibrarian_phone_num }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" value="{{ profile.sublibrarian_address }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your address.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% elif role == "librarycommander" %}
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" value="{{ profile.librarycommander_phone_num }}" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" value="{{ profile.librarycommander_address }}" required>
                                            <div class="invalid-feedback">
                                                Please enter your address.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between mt-4">
                                <a href="/{{role}}/profile/" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Cropping Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><i class="fas fa-crop-alt me-2"></i>Crop Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="img-container">
                    <img id="imagePreview" src="" alt="Preview" class="img-fluid">
                </div>
                <div class="mt-3">
                    <p class="text-muted small">Drag to reposition. Use the handles to resize the crop area. The image will be cropped to a 6:1 ratio.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="cropButton">
                    <i class="fas fa-crop me-2"></i>Crop & Save
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Cropper.js -->
<script src="https://cdn.jsdelivr.net/npm/cropperjs/dist/cropper.min.js"></script>

<script>
    // Form validation
    (function () {
        'use strict';

        // Fetch all forms with the 'needs-validation' class
        const forms = document.querySelectorAll('.needs-validation');

        // Loop over them and prevent submission if validation fails
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();

                    // Show SweetAlert for validation errors
                    Swal.fire({
                        icon: 'warning',
                        title: 'Form Validation Error',
                        text: 'Please fill in all required fields correctly.',
                        confirmButtonColor: '#28345a'
                    });
                }

                form.classList.add('was-validated');
            }, false);
        });
    })();

    // Image handling and cropping
    document.addEventListener("DOMContentLoaded", function () {
        const imageInput = document.getElementById("image");
        const imagePreviewContainer = document.getElementById("imagePreviewContainer");
        const uploadPlaceholder = document.getElementById("uploadPlaceholder");
        const currentImagePreview = document.getElementById("currentImagePreview");
        let cropper;

        // Check if there's an existing image
        if (currentImagePreview.getAttribute('src') &&
            currentImagePreview.getAttribute('src') !== 'undefined' &&
            currentImagePreview.getAttribute('src') !== '' &&
            !currentImagePreview.getAttribute('src').includes('None')) {
            currentImagePreview.style.display = 'block';
            uploadPlaceholder.style.display = 'none';
        } else {
            currentImagePreview.style.display = 'none';
            uploadPlaceholder.style.display = 'block';
        }

        // Handle image file selection
        imageInput.addEventListener("change", function (e) {
            const file = e.target.files[0];
            if (file) {
                // Validate image size
                const maxSize = 100 * 1024; // 100 KB in bytes
                if (file.size > maxSize) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Image Too Large',
                        text: 'The image size exceeds the 100KB limit. Please choose a smaller image.',
                        confirmButtonColor: '#d33',
                    });
                    e.target.value = ''; // Reset the file input
                    return;
                }

                const reader = new FileReader();
                reader.onload = function (event) {
                    const imageURL = event.target.result;
                    const imagePreview = document.getElementById("imagePreview");

                    // Set the image preview
                    imagePreview.src = imageURL;

                    // Initialize the modal and cropper
                    const modal = new bootstrap.Modal(document.getElementById("imageModal"));
                    modal.show();

                    if (cropper) {
                        cropper.destroy();
                    }

                    // Initialize Cropper.js with a 6:1 aspect ratio
                    cropper = new Cropper(imagePreview, {
                        aspectRatio: 6 / 1,
                        viewMode: 1,
                        autoCropArea: 0.8,
                        zoomable: true,
                        scalable: false,
                        guides: true,
                        highlight: true,
                        dragMode: 'move'
                    });
                };
                reader.readAsDataURL(file);
            }
        });

        // Handle crop button click
        document.getElementById("cropButton").addEventListener("click", function () {
            if (!cropper) return;

            const croppedCanvas = cropper.getCroppedCanvas({
                width: 600,
                height: 100,
                minWidth: 300,
                minHeight: 50,
                maxWidth: 1200,
                maxHeight: 200,
                fillColor: '#fff',
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            croppedCanvas.toBlob(function (blob) {
                // Create a URL for the blob
                const croppedImageUrl = URL.createObjectURL(blob);

                // Update the preview image
                currentImagePreview.src = croppedImageUrl;
                currentImagePreview.style.display = 'block';
                uploadPlaceholder.style.display = 'none';

                // Add the cropped image to the form input
                const inputFile = document.getElementById("image");
                const dataTransfer = new DataTransfer();
                const fileName = 'profile_image.png';
                const croppedFile = new File([blob], fileName, { type: 'image/png' });

                dataTransfer.items.add(croppedFile);
                inputFile.files = dataTransfer.files;

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Image Cropped Successfully',
                    text: 'The image has been cropped and is ready to save with your profile.',
                    confirmButtonColor: '#28345a',
                    timer: 2000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });

                // Close the modal after cropping
                const modal = bootstrap.Modal.getInstance(document.getElementById("imageModal"));
                modal.hide();
            }, 'image/png', 0.9); // 90% quality
        });

        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    });
</script>
</body>
</html>
