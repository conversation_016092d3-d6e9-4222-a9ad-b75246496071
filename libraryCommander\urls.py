from django.urls import path
from . import views


urlpatterns = [
    path("signup/", views.librarycommander_signup, name="signup"),
    path("login/", views.librarycommander_login, name="login"),
    path("logout/", views.librarycommander_logout, name="logout"),
    path("dashboard/", views.library_commander_dashboard, name="dashboard"),
    path("profile/", views.librarycommander_profile, name="profile"),
    path("edit-profile/", views.edit_library_commander_profile, name="edit_profile"),
    path(
        "table/",
        views.library_and_manager_data,
        name="manager-library-data",
    ),
    path("complaint_dashboard/", views.complaint_page, name="complaint_page"),
    path("logs/", views.download_log, name="download_logs"),  # Render HTML template
    path("logs/<str:log_type>/", views.download_log, name="download_log"),
    path("backups/", views.list_and_download_backups, name="list_backups"),
    path("restore/", views.restore_database, name="restore_database"),
    path(
        "backups/<str:filename>/",
        views.list_and_download_backups,
        name="download_backup",
    ),
    path(
        "get-ticket-details/<int:ticket_id>/",
        views.get_ticket_details,
        name="get_ticket_details",
    ),
    path(
        "update-ticket-status/", views.update_ticket_status, name="update_ticket_status"
    ),
    path("feedback/", views.feedback_page, name="feedback_page"),
    path("blog_detail/<slug:slug>/", views.blog_details, name="blog_details"),
]
