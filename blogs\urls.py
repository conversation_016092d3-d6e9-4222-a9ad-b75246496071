from django.urls import path
from . import views
from django.views.generic.base import TemplateView


# urlpatterns = [
#     path("", views.blog_list, name="blog_data"),
#     path("create-blog/", views.blog_create, name="create"),  # type: ignore
#     path("<slug:slug>/", views.blog_detail, name="blog_data"),
#     path("p", views.blogs_display, name="blogs_display"),
#     path("p/cat/<str:category>/", views.cat_blogs_display, name="cat_blogs_display"),
#     path("p/<slug:slug>/", views.blog_display, name="blog_display"),
#     path("update/<slug:slug>/", views.blog_update, name="update"),
#     # path("doupdate/<int:id>", views.blog_doupdate, name="doupdate"),
#     path("delete/<slug:slug>/", views.blog_delete, name="delete"),
# ]




app_name = "blogs"

urlpatterns = [
    # List and Display URLs
    path("", views.blog_list, name="blog_data"),
    path("p/", views.blogs_display, name="blogs_display"),
    # Category URLs
    path("p/cat/<str:category>/", views.cat_blogs_display, name="cat_blogs_display"),
    # Search URL
    path("search/", views.blog_search, name="blog_search"),
    # Blog Management URLs
    path("create-blog/", views.blog_create, name="create"),
    path("update/<slug:slug>/", views.blog_update, name="update"),
    path("delete/<slug:slug>/", views.blog_delete, name="delete"),
    # Single Blog Display URLs
    path("p/<slug:slug>/", views.blog_display, name="blog_display"),  # Public view
    path("<slug:slug>/", views.blog_detail, name="blog_data"),  # Admin view
    path("blog_myfunction", views.blog_myfunction, name="blog_myfunction"),
    # path('coupons/', views.CouponListView.as_view(), name='coupon_list'),
    path('coupons/create_coupon/', views.CouponView.as_view(), name="coupon_view"),
    path('coupons/coupon-form/',views.CouponFormView.as_view(), name="coupon_form"),
    path('coupons/coupon_delete/<pk>',views.CouponDeleteView.as_view(), name="coupon_delete"),
    path('coupons/coupon_detail_view/<pk>',views.coupon_detail_view, name="coupon_detail_view"),
    # path('blog_details/<slug:slug>/',views.blog_details, name="blog_details"),
]
