<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- Primary Meta Tags -->
    <title>{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights</title>

    <meta name="description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="metaDescription">

    <meta name="keywords" content="{% if category %}{{ category }}, {{ category }} articles, {{ category }} library resources, {{ category }} library management{% elif tag %}{{ tag }}, {{ tag }} library, {{ tag }} resources, articles about {{ tag }}{% else %}library blog, library management, student resources, educational content, library science, digital libraries, library technology, reading resources, study spaces, library administration{% endif %}, Librainian blog, library resources">

    <meta name="author" content="Librainian">
    <meta name="robots" content="index, follow, max-image-preview:large">

    <!-- Canonical URL -->
    <link rel="canonical" href="{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}">

    <!-- Alternate Language URLs -->
    <link rel="alternate" hreflang="en" href="{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}">
    <link rel="alternate" hreflang="x-default" href="https://www.librainian.com/blogs/p/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian Blog">
    <meta property="og:title" content="{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights">
    <meta property="og:description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="ogDescription">
    <meta property="og:url" content="{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}">
    <meta property="og:image" content="https://www.librainian.com/static/img/blog-header-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights">
    <meta name="twitter:description" content="{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %} Stay informed with the latest trends in library science." id="twitterDescription">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/blog-header-image.jpg">

    <!-- Structured Data - Blog -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Blog",
      "name": "{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %}",
      "description": "{% if category %}Explore our {{ category|title }} articles and resources for libraries and educational institutions. {% elif tag %}Browse all articles tagged with {{ tag|title }} on the Librainian blog. {% else %}Discover insights, tips, and best practices for library management, student engagement, and educational resources on the Librainian blog.{% endif %}",
      "url": "{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% elif tag %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% else %}https://www.librainian.com/blogs/p/{% endif %}",
      "publisher": {
        "@type": "Organization",
        "name": "Librainian",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
        }
      },
      "blogPost": [
        {% for blog in blogs %}
        {
          "@type": "BlogPosting",
          "headline": "{{ blog.title }}",
          "description": "{{ blog.description|truncatewords:30|striptags }}",
          "image": "{{ blog.image.url }}",
          "url": "https://www.librainian.com/blogs/p/{{ blog.slug }}/",
          "datePublished": "{{ blog.date_created|date:'c' }}",
          "dateModified": "{{ blog.date_updated|date:'c' }}",
          "author": {
            "@type": "Person",
            "name": "{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}"
          },
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://www.librainian.com/blogs/p/{{ blog.slug }}/"
          }
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
      ]
    }
    </script>

    <!-- Structured Data - BreadcrumbList -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.librainian.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Blog",
          "item": "https://www.librainian.com/blogs/p/"
        }{% if category or tag %},
        {
          "@type": "ListItem",
          "position": 3,
          "name": "{% if category %}{{ category|title }}{% else %}{{ tag|title }}{% endif %}",
          "item": "{% if category %}https://www.librainian.com/blogs/category/{{ category|slugify }}/{% else %}https://www.librainian.com/blogs/tag/{{ tag|slugify }}/{% endif %}"
        }{% endif %}
      ]
    }
    </script>

    <title>{% if category %}{{ category|title }} Articles{% elif tag %}{{ tag|title }} Articles{% else %}Librainian Blog{% endif %} | Library Management Insights</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">

    <script>

        function trimDescription(description, maxLength) {
            if (description.length > maxLength) {
                return description.substring(0, maxLength - 3) + "..."; // 3 dots ke liye space chhodna
            }
            return description;
        }

        document.addEventListener("DOMContentLoaded", function () {
            var metaDescription = "{{ blog.description|escapejs }}"; // Escape JS to avoid issues
            var trimmedDescription = trimDescription(metaDescription, 160);

            // Set the content for meta tags
            document.getElementById("metaDescription").setAttribute("content", trimmedDescription);
            document.getElementById("ogDescription").setAttribute("content", trimmedDescription);
            document.getElementById("twitterDescription").setAttribute("content", trimmedDescription);
        });
    </script>

  

        <!-- disable Print Screen for Windows -->

          

        <!-- disable print screen for mac -->

          

        <!-- disabling print screen for Linux -->

          

        <!-- disabling inspection tool -->

          


        <style>
        /* CSS Variables for consistent styling */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --secondary-color: #1e293b;
            --accent-color: #f59e0b;
            --background-color: #f8fafc;
            --text-color: #334155;
            --text-light: #64748b;
            --text-dark: #0f172a;
            --border-color: #e2e8f0;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --transition: all 0.3s ease;
            --border-radius: 0.5rem;
        }

        /* Base Styles */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            color: var(--text-dark);
            font-weight: 600;
            line-height: 1.3;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        a:hover {
            color: var(--primary-dark);
        }

        /* Header Styles */
        header {
            background-color: var(--secondary-color);
            color: #fff;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.25rem;
        }

        .navbar a {
            color: #fff;
            text-decoration: none;
            margin: 0 0.9rem;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .navbar a:hover {
            color: var(--accent-color);
        }

        .btn-outline-info {
            border: 1px solid #fff;
            border-radius: 2rem;
            padding: 0.35rem 1rem;
        }

        .btn-info {
            background-color: var(--primary-color);
            border-radius: 2rem;
            padding: 0.35rem 1rem;
            border: none;
        }

        /* Blog Header Section */
        .blog-header {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
            padding: 2.5rem 0;
        }

        .blog-title {
            font-size: 2.25rem;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
        }

        .lead {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 1.25rem;
        }

        /* Breadcrumbs */
        .breadcrumb {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            font-size: 0.9rem;
        }

        .breadcrumb-item {
            display: inline-flex;
            align-items: center;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "/";
            display: inline-block;
            margin: 0 0.5rem;
            color: var(--text-light);
        }

        .breadcrumb-item a {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-light);
        }

        /* Search Form */
        .search-form {
            width: 100%;
        }

        .search-form .form-control {
            border-radius: 2rem 0 0 2rem;
            border: 1px solid var(--border-color);
            padding: 0.6rem 1.25rem;
            font-size: 0.95rem;
        }

        .search-form .btn {
            border-radius: 0 2rem 2rem 0;
            padding: 0.6rem 1.25rem;
        }

        /* Category and Tag Navigation */
        .filter-heading {
            font-size: 1rem;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
        }

        .category-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .category-buttons .btn {
            border-radius: 2rem;
            font-size: 0.85rem;
            padding: 0.35rem 1rem;
            font-weight: 500;
        }

        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 0.5rem;
        }

        .tag-link {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background-color: #f1f5f9;
            border-radius: 2rem;
            font-size: 0.85rem;
            color: var(--text-light);
            transition: var(--transition);
        }

        .tag-link:hover, .tag-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Blog Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            box-shadow: var(--card-shadow);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .card-img-wrapper {
            position: relative;
            overflow: hidden;
        }

        .card-img-top {
            height: 200px;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .card:hover .card-img-top {
            transform: scale(1.05);
        }

        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .category-badge {
            display: inline-block;
            padding: 0.15rem 0.5rem;
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .card-title {
            font-size: 1.1rem;
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }

        .card-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .card-meta i {
            margin-right: 0.25rem;
        }

        .card-text {
            font-size: 0.9rem;
            color: var(--text-color);
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tag-badge {
            display: inline-block;
            font-size: 0.75rem;
            color: var(--text-light);
        }

        .tag-badge:hover {
            color: var(--primary-color);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }

        .page-item {
            margin: 0 0.25rem;
        }

        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-link:hover {
            background-color: #f1f5f9;
            color: var(--primary-color);
        }

        /* Newsletter Box */
        .newsletter-box {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }

        .newsletter-box h4 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .newsletter-form .form-control {
            border-radius: 2rem 0 0 2rem;
            padding: 0.6rem 1.25rem;
        }

        .newsletter-form .btn {
            border-radius: 0 2rem 2rem 0;
        }

        /* No Results */
        .no-results {
            padding: 3rem 0;
            text-align: center;
        }

        .no-results h3 {
            margin: 1rem 0;
            font-size: 1.5rem;
        }

        .no-results p {
            margin-bottom: 1.5rem;
            color: var(--text-light);
        }

        /* Footer */
        footer {
            background-color: var(--secondary-color);
            color: #fff;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        footer .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin: 0 0.75rem;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        footer a:hover {
            color: white;
        }

        /* Loader */
        #loader {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loader {
            width: 3rem;
            height: 3rem;
            border: 0.25rem solid rgba(59, 130, 246, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .blog-title {
                font-size: 1.75rem;
            }

            .lead {
                font-size: 1rem;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                padding: 1rem;
            }

            .navbar-brand {
                margin-bottom: 1rem;
            }

            .d-flex {
                flex-wrap: wrap;
                justify-content: center;
            }

            .navbar-menu {
                margin-bottom: 0.5rem;
            }

            .blog-header {
                padding: 1.5rem 0;
                text-align: center;
            }

            .search-form {
                margin-top: 1.5rem;
            }

            .newsletter-box {
                text-align: center;
            }

            .newsletter-form {
                margin-top: 1rem;
            }

            footer .container {
                flex-direction: column;
                gap: 1rem;
            }
        }

        @media (max-width: 576px) {
            .blog-title {
                font-size: 1.5rem;
            }

            .category-buttons, .tag-cloud {
                justify-content: center;
            }
        }
    </style>
</head>

<body>

    <!-- Navbar -->
    <header>
        <nav class="navbar">
          <div class="navbar-brand text-white">
            <a href="/blogs/p">Blog</a>
          </div>
          <div class="d-flex justify-content-end align-items-center">
            <div class="navbar-menu">
              <a href="/">Home</a>
            </div>
            <div class="navbar-menu">
              <a href="/librarian/library-list/">Library List</a>
            </div>
            <div class="navbar-menu">
              <a href="/membership/plans/">Pricing</a>
            </div>
            <div class="navbar-menu">
              <a href="/librarian/login/" class="btn btn-outline-info">Login</a>
            </div>
            <div class="navbar-menu">
              <a href="/librarian/signup/" class="btn btn-info">Signup</a>
            </div>
          </div>
        </nav>
      </header>


    <!-- Blog Header Section -->
    <div class="blog-header py-4 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="blog-title">
                        {% if category %}{{ category|title }} Articles
                        {% elif tag %}Articles Tagged with "{{ tag|title }}"
                        {% else %}Librainian Blog
                        {% endif %}
                    </h1>
                    <p class="lead text-muted">
                        {% if category %}Explore our collection of articles about {{ category|lower }}.
                        {% elif tag %}Browse all content related to {{ tag|lower }}.
                        {% else %}Discover insights, tips, and best practices for library management.
                        {% endif %}
                    </p>
                    <!-- Breadcrumbs -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-transparent p-0">
                            <li class="breadcrumb-item"><a href="/">Home</a></li>
                            <li class="breadcrumb-item"><a href="/blogs/p/">Blog</a></li>
                            {% if category %}
                            <li class="breadcrumb-item active" aria-current="page">{{ category|title }}</li>
                            {% elif tag %}
                            <li class="breadcrumb-item active" aria-current="page">{{ tag|title }}</li>
                            {% endif %}
                        </ol>
                    </nav>
                </div>
                <div class="col-md-4">
                    <form class="search-form" onsubmit="return false;">
                        <div class="input-group">
                            <input class="form-control" type="search" id="searchInput" placeholder="Search articles..." aria-label="Search" oninput="filterBlogs()">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Category and Tag Navigation -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="category-nav">
                    <h5 class="filter-heading">Browse by Category:</h5>
                    <div class="category-buttons">
                        <a href="/blogs/p/" class="btn btn-sm {% if not category %}btn-primary{% else %}btn-outline-primary{% endif %} mr-2 mb-2">All</a>
                        {% for cat in categories %}
                        <a href="/blogs/category/{{ cat.slug }}/" class="btn btn-sm {% if category == cat.name %}btn-primary{% else %}btn-outline-primary{% endif %} mr-2 mb-2">{{ cat.name }}</a>
                        {% endfor %}
                    </div>
                </div>

                {% if popular_tags %}
                <div class="tag-nav mt-3">
                    <h5 class="filter-heading">Popular Tags:</h5>
                    <div class="tag-cloud">
                        {% for t in popular_tags %}
                        <a href="/blogs/tag/{{ t.slug }}/" class="tag-link {% if tag == t.name %}active{% endif %}">
                            #{{ t.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Blog Posts -->
        <div class="row">
            <div class="col-md-12">
                <div id="blogContainer" class="row">
                    {% for blog in blogs %}
                    <div class="col-md-4 col-lg-3 blog-card mb-4"
                         data-categories="{% for cat in blog.categories.all %}{{ cat.name|lower }} {% endfor %}"
                         data-tags="{% for t in blog.tags.all %}{{ t.name|lower }} {% endfor %}">
                        <div class="card h-100 shadow-sm">
                            <div class="card-img-wrapper">
                                <img src="{{ blog.image.url }}" class="card-img-top" alt="{{ blog.title }}" loading="lazy">
                                {% if blog.featured %}
                                <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                {% endif %}
                            </div>
                            <div class="card-body d-flex flex-column">
                                <!-- Categories -->
                                <div class="card-categories mb-2">
                                    {% for cat in blog.categories.all|slice:":2" %}
                                    <a href="/blogs/category/{{ cat.slug }}/" class="category-badge">{{ cat.name }}</a>
                                    {% endfor %}
                                </div>

                                <h5 class="card-title">{{ blog.title }}</h5>

                                <div class="card-meta mb-2">
                                    <span class="author">
                                        <i class="fas fa-user-circle"></i> {{ blog.author.user.first_name }} {{ blog.author.user.last_name }}
                                    </span>
                                    <span class="date">
                                        <i class="far fa-calendar-alt"></i> {{ blog.date_created|date:"M d, Y" }}
                                    </span>
                                </div>

                                <p class="card-text flex-grow-1">{{ blog.description|truncatewords:20|safe }}</p>

                                <!-- Tags -->
                                <div class="card-tags mb-3">
                                    {% for tag in blog.tags.all|slice:":3" %}
                                    <a href="/blogs/tag/{{ tag.slug }}/" class="tag-badge">#{{ tag.name }}</a>
                                    {% endfor %}
                                </div>

                                <a href="/blogs/p/{{blog.slug}}/" class="btn btn-outline-primary btn-sm mt-auto" aria-label="Read full article: {{ blog.title }}">
                                    Read Article <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center py-5">
                        <div class="no-results">
                            <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                            <h3>No articles found</h3>
                            <p class="text-muted">
                                {% if category %}No articles found in the "{{ category }}" category.
                                {% elif tag %}No articles found with the tag "{{ tag }}".
                                {% else %}No articles match your search criteria.
                                {% endif %}
                            </p>
                            <a href="/blogs/p/" class="btn btn-primary mt-3">View All Articles</a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Blog pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>

        <!-- Newsletter Signup -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="newsletter-box bg-light p-4 rounded">
                    <div class="row align-items-center">
                        <div class="col-md-7">
                            <h4>Subscribe to Our Newsletter</h4>
                            <p class="mb-md-0">Get the latest library management insights delivered to your inbox.</p>
                        </div>
                        <div class="col-md-5">
                            <form class="newsletter-form" id="newsletterForm">
                                <div class="input-group">
                                    <input type="email" class="form-control" placeholder="Your email address" required>
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Subscribe</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer>
        <div class="container">
            <div>&copy; 2024 Librainian. All rights reserved.</div>
            <div>
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact Us</a>
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize search and filtering
            const searchInput = document.getElementById('searchInput');
            const blogCards = document.querySelectorAll('.blog-card');
            const noResultsElement = document.querySelector('.no-results');

            // Focus search input when page loads
            if (searchInput) {
                setTimeout(() => {
                    searchInput.focus();
                }, 500);
            }

            // Newsletter form submission
            const newsletterForm = document.getElementById('newsletterForm');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const emailInput = this.querySelector('input[type="email"]');
                    if (emailInput && emailInput.value) {
                        // Track newsletter signup
                        if (typeof gtag === 'function') {
                            gtag('event', 'newsletter_signup', {
                                'event_category': 'Engagement',
                                'event_label': 'Blog Newsletter'
                            });
                        }

                        // Show success message
                        const formParent = this.parentNode;
                        this.style.display = 'none';
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success mt-2';
                        successMessage.textContent = 'Thank you for subscribing! We\'ll be in touch soon.';
                        formParent.appendChild(successMessage);
                    }
                });
            }

            // Enhanced search functionality
            function filterBlogs() {
                const input = searchInput.value.toLowerCase().trim();
                let foundResults = false;

                // If search is empty, show all cards
                if (input === '') {
                    blogCards.forEach(card => {
                        card.style.display = '';
                    });
                    if (noResultsElement) noResultsElement.style.display = 'none';
                    return;
                }

                // Filter cards based on search input
                blogCards.forEach(card => {
                    const title = card.querySelector('.card-title').textContent.toLowerCase();
                    const description = card.querySelector('.card-text').textContent.toLowerCase();
                    const categories = card.getAttribute('data-categories') || '';
                    const tags = card.getAttribute('data-tags') || '';
                    const author = card.querySelector('.author') ? card.querySelector('.author').textContent.toLowerCase() : '';

                    // Check if any content matches the search term
                    if (title.includes(input) ||
                        description.includes(input) ||
                        categories.includes(input) ||
                        tags.includes(input) ||
                        author.includes(input)) {
                        card.style.display = '';
                        foundResults = true;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide no results message
                if (noResultsElement) {
                    noResultsElement.style.display = foundResults ? 'none' : 'block';
                }

                // Track search with no results
                if (!foundResults && typeof gtag === 'function' && input.length > 2) {
                    gtag('event', 'search_no_results', {
                        'event_category': 'Search',
                        'event_label': input
                    });
                }
            }

            // Attach search event listener
            if (searchInput) {
                searchInput.addEventListener('input', filterBlogs);
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterBlogs();
                        // Track search event
                        if (typeof gtag === 'function' && this.value.trim().length > 0) {
                            gtag('event', 'blog_search', {
                                'event_category': 'Search',
                                'event_label': this.value.trim()
                            });
                        }
                    }
                });
            }

            // Track category and tag clicks
            const categoryLinks = document.querySelectorAll('.category-badge, .category-buttons .btn');
            categoryLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (typeof gtag === 'function') {
                        gtag('event', 'category_click', {
                            'event_category': 'Navigation',
                            'event_label': this.textContent.trim()
                        });
                    }
                });
            });

            const tagLinks = document.querySelectorAll('.tag-badge, .tag-link');
            tagLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (typeof gtag === 'function') {
                        gtag('event', 'tag_click', {
                            'event_category': 'Navigation',
                            'event_label': this.textContent.trim()
                        });
                    }
                });
            });

            // Track article clicks
            const articleLinks = document.querySelectorAll('.card a[href^="/blogs/p/"]');
            articleLinks.forEach(link => {
                link.addEventListener('click', function() {
                    const cardTitle = this.closest('.card').querySelector('.card-title').textContent;
                    if (typeof gtag === 'function') {
                        gtag('event', 'article_click', {
                            'event_category': 'Content',
                            'event_label': cardTitle
                        });
                    }
                });
            });
        });
    </script>
</body>
<script>
    window.addEventListener("load", function () {
        const path = window.location.pathname; // Get current page path
        let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

        // Increment count for the current path
        pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

        // Store updated data back to localStorage
        localStorage.setItem("page_data", JSON.stringify(pageData));
      });

      // Function to send page view data
      function sendPageData() {
        const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

        if (Object.keys(pageData).length > 0) {
          fetch(location.origin + "/librarian/track-page-view/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": "{{ csrf_token }}",
            },
            body: JSON.stringify(pageData),
          })

            .then(() => {
              localStorage.removeItem("page_data");
            })
            .catch((error) => console.error("Error sending page data:", error));
            localStorage.removeItem("page_data");
        } else {

          console.log("No page data to send");
        }
      }

      // Send data every 10 seconds
      setInterval(sendPageData, 10000);
</script>
</html>