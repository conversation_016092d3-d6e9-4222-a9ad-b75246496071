<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Bootstrap CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <title>Coupon List</title>
    <style>
        body {
            -webkit-user-select: none;
            user-select: none;
            margin: 16px;
            font-family: 'Comfortaa', sans-serif !important;
            background-color: #9bc6bf;
        }
        .card {
            border-radius: 12px;
            transition: 0.3s;
        }
        .card:hover {
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    {% include "sidebar.html" %}
    <div class="container">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-md-8 col-12">
                    <h2 class="p-3 text-white text-center" style="background-color: #294282; border-radius: 1rem;">All Coupons</h2>
                    <a href="/blogs/coupons/coupon-form/" class="btn btn-sm btn-warning rounded-4 my-3">
                        <i class="fas fa-plus-circle"></i> create Coupon
                    </a>
                                    <!-- Messages Section -->
                    {% if messages %}
                    <div id="messageContainer">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="row">
                        {% for coupon in coupons %}
                        <div class="col-md-4">
                            <div class="card p-3 mb-4">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-ticket-alt"></i> {{ coupon.code }}
                                    </h5>
                                    <p class="card-text">
                                        <strong>Discount:</strong>
                                        {%if coupon.discount_type == 'amount'%} ₹ {{ coupon.discount }} {%else%} {{ coupon.discount }}% {%endif%}  <br>
                                        <strong>Expiry Date:</strong> {{ coupon.expiry_date }} <br>
                                        <strong>Usage Limit:</strong> {{ coupon.usage_limit }} <br>
                                        <strong>Overall Discount:</strong> ₹{{ coupon.overallDiscountAmount }}
                                    </p>
                                    <div class="d-flex justify-content-between">
                                        <a href="/blogs/coupons/coupon_detail_view/{{coupon.id}}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="/blogs/coupons/coupon_delete/{{coupon.id}}" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?');">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <p class="text-center">No coupons available.</p>
                        {% endfor %}
                    </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper (includes all Bootstrap JS) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
</body>
</html>
