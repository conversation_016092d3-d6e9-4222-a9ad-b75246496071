from django.contrib.sitemaps import Sitemap
from librarian.models import Librarian_param
from django.urls import reverse


class PostSitemaps(Sitemap):
    changefreq = "daily"
    priority = 0.7

    def items(self):
        items = Librarian_param.objects.all()
        print(f"Number of items: {items.count()}")
        return items

    def lastmod(self, obj):
        return obj.user.date_joined

    def location(self, obj):
        url = obj.get_absolute_url()

        print(f"Generated URL for {obj.library_name}: {url}")
        return url


class LibrarianStaticViewSitemap(Sitemap):
    priority = 0.64
    changefreq = "weekly"

    def items(self):
        return [
            "lib_signup",
            "lib_login",
        ]

    def location(self, item):
        return reverse(item)
