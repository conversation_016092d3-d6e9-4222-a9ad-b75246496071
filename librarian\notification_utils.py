from django.contrib.auth.models import User
from firebase_admin import messaging
from .models import DeviceToken


def send_fcm_notification(
    user=None, tokens=None, title="Notification", body="", data=None
):
    """
    Send FCM notification to specific user or list of tokens

    :param user: User instance, User ID, or username
    :param tokens: List of device tokens (optional)
    :param title: Notification title
    :param body: Notification body
    :param data: Additional data payload (optional)
    :return: Messaging response
    """
    # Convert user to User instance if it's an ID or username
    if user:
        if isinstance(user, (int, str)):
            try:
                # Try to get user by ID or username
                user = (
                    User.objects.get(id=user)
                    if isinstance(user, int)
                    else User.objects.get(username=user)
                )
            except User.DoesNotExist:
                print(f"User not found: {user}")
                return None

    
    # Retrieve tokens if user is provided
    if user:
        tokens = list(
            DeviceToken.objects.filter(user=user).values_list("token", flat=True)
        )

    # Validate tokens
    if not tokens:
        print(f"No device tokens found for user: {user}")
        return None

    # Ensure data is a dictionary
    if data is None:
        data = {}

    # Prepare notification
    notification = messaging.MulticastMessage(
        notification=messaging.Notification(title=title, body=body),
        tokens=tokens,
        data=data,  
    )

    # Send notification
    try:
        response = messaging.send_multicast(notification)
        print(f"{response.success_count} messages were sent successfully")
        return response
    except Exception as e:
        print(f"Error sending notification: {e}")
        return None


def get_user_device_tokens(user):
    """
    Helper function to get device tokens for a specific user

    :param user: User instance, User ID, or username
    :return: List of device tokens
    """
    # Convert user to User instance if it's an ID or username
    if isinstance(user, (int, str)):
        try:
            user = (
                User.objects.get(id=user)
                if isinstance(user, int)
                else User.objects.get(username=user)
            )
        except User.DoesNotExist:
            print(f"User not found: {user}")
            return []

    # Retrieve and return tokens
    return list(DeviceToken.objects.filter(user=user).values_list("token", flat=True))


def send_notification_to_all_users(title="Notification", body="", data=None):
    """
    Send notification to all registered device tokens
    """
    tokens = list(DeviceToken.objects.values_list("token", flat=True))
    if tokens:
        return send_fcm_notification(tokens=tokens, title=title, body=body, data=data)
