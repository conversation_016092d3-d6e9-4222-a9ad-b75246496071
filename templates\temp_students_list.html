    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar --> 

    {% include "sidebar.html" %}

    <br/>

    <div class="container-fluid py-4">
        <div class="card" style="background: #fff !important;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0 fs-1 text-start">Pending Students </h3>
            </a>
            </div>

            <div class="card-body">
                {% if messages %}
                    <div class="alert alert-success alert-dismissible fade show fs-6" role="alert">
                        {% for message in messages %}
                            {{ message }}
                        {% endfor %}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endif %}

                <!-- Desktop Table View -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover table-striped align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th class="fs-6">Name</th>
                                <th class="fs-6">Email</th>
                                <th class="fs-6">Mobile</th>
                                <th class="fs-6">State</th>
                                <th class="fs-6">Course</th>
                                <th class="fs-6">Status</th>
                                <th class="fs-6">Complete</th>
                                <th class="fs-6">Edit</th>
                                <th class="fs-6">Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in temp_students %}
                                <tr>
                                    <td class="fs-6">{{ student.name }}</td>
                                    <td class="fs-6">{{ student.email }}</td>
                                    <td class="fs-6">{{ student.mobile }}</td>
                                    <td class="fs-6">{{ student.state.name }}</td>
                                    <td class="fs-6">{{ student.course.name }}</td>
                                    <td>
                                        <span class="badge p-2 rounded-2 text-white {% if student.status == 'Active' %}bg-success{% else %}bg-secondary{% endif %}">
                                            {{ student.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="student_id" value="{{ student.id }}">
                                            <button
                                                type="submit"
                                                name="status"
                                                value="completed"
                                                class="btn btn-primary btn-sm"
                                                {% if student.status == 'Completed' %}disabled{% endif %}
                                            >
                                                Complete
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <a href="{% url 'update_temp_student' student.id %}">Edit</a>
                                    </td>
                                    <td>
                                        <a href="{% url 'delete_temp_student' student.id %}" onclick="return confirm('Are you sure?')">Delete</a>

                                    </td>

                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="d-block d-md-none">
                    {% for student in temp_students %}
                        <div class="card mb-3 shadow-sm student-card" data-student-id="{{ student.id }}" data-status="{{ student.status }}" style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="card-body position-relative">
                                <!-- Three Dots Menu -->
                                <div class="dropdown position-absolute" style="top: 15px; right: 15px;">
                                    <button class="btn btn-link p-0 text-muted" type="button" id="dropdownMenuButton{{ student.id }}" data-bs-toggle="dropdown" aria-expanded="false" onclick="event.stopPropagation();">
                                        <i class="fas fa-ellipsis-v fs-5"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton{{ student.id }}">
                                        <li>
                                            <a class="dropdown-item" href="{% url 'update_temp_student' student.id %}">
                                                <i class="fas fa-edit me-2 text-primary"></i>Update
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="{% url 'delete_temp_student' student.id %}" onclick="return confirm('Are you sure you want to delete this student?')">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Hidden form for complete action -->
                                <form method="POST" class="complete-form" style="display: none;">
                                    {% csrf_token %}
                                    <input type="hidden" name="student_id" value="{{ student.id }}">
                                    <input type="hidden" name="status" value="completed">
                                </form>

                                <!-- Card Content -->
                                <div class="clickable-area" style="padding-right: 40px;">
                                    <h5 class="card-title fs-5 mb-3 fw-bold text-primary">{{ student.name }}</h5>

                                    <div class="row">
                                        <div class="col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-phone me-2 text-muted"></i>
                                                <span class="fs-6"><strong>Mobile:</strong> {{ student.mobile }}</span>
                                            </div>
                                        </div>

                                        <div class="col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-graduation-cap me-2 text-muted"></i>
                                                <span class="fs-6"><strong>Course:</strong> {{ student.course.name }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Visual indicator for clickable action -->
                                    {% if student.status != 'completed' %}
                                        <div class="mt-3 text-center">
                                            <small class="text-muted">
                                                <i class="fas fa-hand-pointer me-1"></i>Tap to complete
                                            </small>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>



    <!-- Add Student Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Student</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label class="form-label">Full Name</label>
                            <input type="text" class="form-control" required>
                        </div>
                        <!-- <div class="mb-3">
                            <label class="form-label">Father's Name</label>
                            <input type="text" class="form-control" required>
                        </div> -->
                        <!-- Additional form fields would go here -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Save Student</button>
                </div>
            </div>
        </div>
    </div>

<!-- Custom CSS for enhanced mobile cards -->
<style>
    .student-card {
        border-radius: 12px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        border-color: #6f42c1;
    }

    .student-card:active {
        transform: translateY(0);
    }

    .student-card[data-status="completed"] {
        opacity: 0.7;
        cursor: default;
    }

    .student-card[data-status="completed"]:hover {
        transform: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
        border-color: #e9ecef;
    }

    .dropdown-toggle::after {
        display: none;
    }

    .dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border: none;
        min-width: 160px;
    }

    .dropdown-item {
        padding: 10px 16px;
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
    }

    .dropdown-item:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .clickable-area {
        position: relative;
        z-index: 1;
    }

    .dropdown {
        z-index: 10;
    }

    /* Card content styling */
    .card-title {
        color: #2c3e50;
        font-weight: 600;
    }

    .badge {
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .student-card {
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
    }
</style>

<!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Popper.js (necessary for Bootstrap's dropdowns and tooltips) -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>

<!-- Bootstrap 4 JavaScript -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<!-- Custom JavaScript for card interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make cards clickable
    const studentCards = document.querySelectorAll('.student-card');

    studentCards.forEach(function(card) {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on dropdown or its children
            if (e.target.closest('.dropdown')) {
                return;
            }

            // Check if student is already completed
            const status = this.getAttribute('data-status');
            if (status === 'completed') {
                // Show message that student is already completed
                alert('This student has already been completed.');
                return;
            }

            // Find the hidden form for this card and submit it to complete the student
            const completeForm = this.querySelector('.complete-form');
            if (completeForm) {
                // Show confirmation dialog
                if (confirm('Are you sure you want to mark this student as completed? This action will create a permanent student record.')) {
                    completeForm.submit();
                }
            }
        });

        // Add visual feedback on touch/click
        card.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });

        card.addEventListener('touchend', function() {
            this.style.transform = '';
        });
    });

    // Prevent dropdown from closing when clicking on form elements
    const dropdownForms = document.querySelectorAll('.dropdown-menu form');
    dropdownForms.forEach(function(form) {
        form.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Note: Complete action is now handled by card click events above

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(function() {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });
});
</script>

</body>
</html>