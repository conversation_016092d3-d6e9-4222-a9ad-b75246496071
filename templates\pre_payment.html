<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Librarian Membership Checkout</title>
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">



  <style>
    body {
      -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            background-color: #9bc6bf;
            font-family: 'Comfortaa', sans-serif !important;
    }
    .card {
      border-radius: 1rem;
      border: 1px solid #ddd;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 20px;
    }
    .card-header {
      background-color: #294282;
      color: white;
      padding-block: 1.4rem;
    }
    .card-body {
      display: flex;
      flex-direction: column;
    }
    .img-section {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    .img-section img {
      max-width: 150px;
      height: auto;
      margin-right: 20px;
      border-radius: 10px;
    }
    .info {
      flex: 1;
    }
    .price-section {
      text-align: right;
    }
    .price-section h5 {
      margin-bottom: 10px;
    }
    .discount {
      font-size: 0.9rem;
      color: #e63946;
    }
    .remove-btn {
      background-color: #ff6b6b;
      border: none;
      border-radius: 5px;
      color: white;
      padding: 10px 20px;
      cursor: pointer;
    }
    .remove-btn:hover {
      background-color: #e63946;
    }
    .pricing-details {
      margin-top: 20px;
    }
    .pricing-details .detail {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .place-order-btn {
      margin-top: 20px;
      text-align: center;
    }
    .place-order-btn button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1rem;
    }
    .place-order-btn button:hover {
      background-color: #0056b3;
    } 

    .card-footer {
      background-color: #294282;
      color: white;
      border-top: 1px solid #ddd;
    }
    h6 {
      color: grey;
    }
    .month-input{
      width: 100%;
      border-radius: 1rem;
    }
    .coupon-input{
      border-top-left-radius: 1rem;
      border-bottom-left-radius: 1rem;
    }
    .apply-btn{
      background-color: #294282;
      font-size: 0.8rem;
      margin-left: -10px;
      border-top-right-radius: 1rem;
      border-bottom-right-radius: 1rem;
    }

    .apply-btn:hover{
      background-color: #1a2c4c;
    }


    @media (max-width: 576px) {
      .img-section {
        flex-direction: column;
        align-items: flex-start;
      }
      .img-section img {
        margin-right: 0;
        margin-bottom: 10px;
      }
      .price-section {
        text-align: left;
      }
      .card-body {
        padding: 1rem;
      }
      .header{
        font-size: 1rem;
      }
    }
    .list-group {
      padding-left: 1rem;
    }
    
  </style>
</head>
<body>

  <div class="container mt-5">
    <div class="row">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header mb-0">
            <h4 class="p-0 m-0 text-center header">Librainian Membership Checkout</h4>
          </div>
          <div class="card-body">
            <h2>{{plan.name}}</h2>
            <div class="img-section">
              <div class="info">
                <ul class="list-group" style="list-style-type: none;">
                  <li><i class="fa-solid fa-circle-check mb-2" style="color: green;"></i> {{plan.description_line_01}}</li>
                  <li><i class="fa-solid fa-circle-check mb-2" style="color: green;"></i> {{plan.description_line_02}}</li>
                  <li><i class="fa-solid fa-circle-check mb-2" style="color: green;"></i> {{plan.description_line_03}}</li>
                </ul>
              </div>
              <div class="price-section ml-md-0 ml-3 mt-md-0 mt-3">
                <h5>₹<span id="unit-price">{{plan.discount_price}}</span></h5>
                <!-- <div class="discount"><del>₹{{plan.price}}</del> 50% off</div> -->
                <button class="remove-btn small px-2 py-1 mt-1"><a href="/membership/plans/" class="text-white" style="text-decoration: none">Remove</a></button>
              </div>
            </div>
            <!-- Section for selecting months -->
            <div class="form-group">
              <label for="num-months">Select Number of Months</label>
              <select id="num-months" class="form-control month-input" onchange="updateTotal()" name="num_months">
                <option value="1">1 Month</option>
                <option value="3">3 Months</option>
                <option value="6">6 Months</option>
                <option value="12">12 Months</option>
              </select>
            </div>
            <div id="showCouponForm" class="text-primary mb-3" style="cursor: pointer;">
              <i class="fas fa-tag"></i> Have a coupon code?
          </div>
            <!-- Coupon code section -->
            <div id="coupon-form-container" class="mb-3" style="display: none;">
              <form id="coupon-form" method="POST" class="card p-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                      <h6 class="mb-0">Apply Coupon Code</h6>
                      <i type="button" class="fas fa-times" id="closeCouponForm" aria-label="Close"></i>
                  </div>
                  {% csrf_token %}
                  <div class="form-group">
                      <div class="input-group">
                          <input type="text" class="form-control coupon-input" id="coupon-code" placeholder="Enter coupon code">
                          <button class="btn btn-primary apply-btn px-5" type="button" onclick="applyCoupon(event)">Apply</button>
                      </div>
                      <div id="coupon-message" class="mt-2 small"></div>
                  </div>
              </form>
          </div>
          </div>
          <div class="card-footer text-center">
            <h6 class="text-white">Items in Cart: <strong>1</strong></h6>
            <h5>Total: ₹<span id="total-price">{{plan.discount_price}}</span></h5>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <div class="pricing-details">
              <div class="detail">
                <h6>Total:</h6>
                <h6>₹<span id="total-amount">{{plan.discount_price}}</span></h6>
              </div>
              <div class="detail">
                <!-- <h6>App discount:</h6> -->
                <!-- <h6 style="color: green;">50%</h6> -->
              </div>
              <div class="detail">
                <h6>Tax <i class="fa-solid fa-circle-info"></i>:</h6>
                <h6>0</h6>
              </div>
              <div class="detail">
                <h6>Order Total:</h6>
                <h6>₹<span id="order-total">{{plan.discount_price}}</span></h6>
              </div>
              <hr>
              <div class="detail">
                <h6>Grand Total:</h6>
                <h6>₹<span id="grand-total-display">{{plan.discount_price}}</span></h6>
              </div>
            </div>
            <div class="place-order-btn">
           <form action="{% url 'create_order' plan.id %}?num_months={{ num_months }}" method="post" id="razorpay-form">
                
              {% csrf_token %}
                <button id="pay-button" type="submit" class="btn btn-success w-100" style="border-radius: 1rem">Checkout</button>
                <input type="hidden" name="plan_id" value="{{ plan.id }}">
                <input type="hidden" name="num_months" id="num-months-hidden">
                <input type="hidden" name="grand_total" id="grand-total" value="{{plan.discount_price}}">
              </form>
            </div>
          </div>
        </div>
        <div class="card p-3 mt-md-4">
          <div class="">
            <h6 class="text-danger small">
            <h6 class="text-danger small" style="line-height: 1.5;">Tax <i class="fa-solid fa-circle-info"></i>: <span>Please note that our business turnover does not exceed the specified limit for GST registration, hence GST is not applicable to our products/services.</span></h6>
          </div>
        </div>
      </div>
    </div>
  </div>



  <script>
    // Coupon form show/hide functionality
    document.getElementById('showCouponForm').addEventListener('click', function() {
        document.getElementById('coupon-form-container').style.display = 'block';
        this.style.display = 'none';
    });

    document.getElementById('closeCouponForm').addEventListener('click', function() {
        document.getElementById('coupon-form-container').style.display = 'none';
        document.getElementById('showCouponForm').style.display = 'block';
        // Clear form and messages when closing
        document.getElementById('coupon-code').value = '';
        document.getElementById('coupon-message').textContent = '';
    });
</script>
  <script>
function updateTotal() {
   const months = parseInt(document.getElementById('num-months').value) || 1;
   const unitPrice = parseInt(document.getElementById('unit-price').textContent) || 0;

   const totalPrice = unitPrice * months;
   const discountedPrice = totalPrice; // No default 50% discount

   // Update displayed prices
   document.getElementById('total-amount').textContent = totalPrice;
   document.getElementById('order-total').textContent = discountedPrice;
   document.getElementById('grand-total-display').textContent = discountedPrice;
   document.getElementById('total-price').textContent = discountedPrice;
   document.getElementById('grand-total').value = discountedPrice;
   document.getElementById('num-months-hidden').value = months;
}

window.onload = updateTotal;

  </script>
  <script>
    function applyCoupon(event) {
      event.preventDefault(); 
        const couponCode = document.getElementById("coupon-code").value;
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        fetch('/membership/validate-coupon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                coupon_code: couponCode
            })
        })
        .then(response => response.json())
        .then(data => {
            const messageDiv = document.getElementById("coupon-message");
            const totalPrice = parseInt(document.getElementById('total-amount').textContent);
            if (data.valid) {
                if (data.discount_type === 'percentage') {
                    const discount = parseInt(data.discount) / 100;
                    const discountedPrice = totalPrice - (totalPrice * discount);
                    document.getElementById('order-total').textContent = discountedPrice;
                    document.getElementById('grand-total-display').textContent = discountedPrice;
                    document.getElementById('total-price').textContent = discountedPrice;
                    document.getElementById('grand-total').value = discountedPrice;
                } else {
                    const discount = parseInt(data.discount);
                    const discountedPrice = totalPrice - discount;
                    document.getElementById('order-total').textContent = discountedPrice;
                    document.getElementById('grand-total-display').textContent = discountedPrice;
                    document.getElementById('total-price').textContent = discountedPrice;
                    document.getElementById('grand-total').value = discountedPrice;
                }
                messageDiv.textContent = "Coupon applied successfully!";
                messageDiv.style.color = "green";
            } else {
                messageDiv.textContent = data.message;
                messageDiv.style.color = "red";
            }
        })
        .catch(error => {
            console.error("Error:", error);
        });
    }
    document.querySelector('.btn.btn-info.mt-2').addEventListener('click', applyCoupon);
    
  </script>
</body>
</html>
