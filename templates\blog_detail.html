<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> Edit {{ blog.title }}</title>
  <meta name="google" content="notranslate">
  <meta name="robots" content="noindex, nofollow">
   
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">


      <!-- Disable Right click -->

        
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }


    .sidebar {
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      width: 250px;
      background-color: #343a40;
      color: #fff;
      padding-top: 20px;
      transition: all 0.3s;
    }

    .sidebar h4 {
      padding-bottom: 10px;
      margin-bottom: 20px;
      border-bottom: 1px solid #495057;
    }

    .sidebar a {
      color: #fff;
      padding: 15px 10px;
      padding-left: 25px;
      display: block;
      text-decoration: none;
      border-left: 3px solid transparent;
      transition: all 0.3s;
    }

    .sidebar a:hover,
    .sidebar a.active {
      background-color: #495057;
      border-left: 3px solid #007bff;

    }

    .main-content {
      margin-left: 250px;
      padding: 4px;
      transition: all 0.3s;
    }

    .navbar {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      background-color: #f0f0f0;
      box-shadow: 0px 4px 6px 1px rgba(150, 142, 150, 1);
    }

    .nav-item a img {
      width: 25px;
      padding-bottom: 4px;
    }

    #dropdown-menu {
      width: 350px;
      padding: 20px;

    }

    hr {
      width: 100%;
      color: black;
      margin-bottom: 0px;
    }

    .viewbtn {
      background-color: #050505;
      width: 90px;
      font-size: 11px;
      color: #fff;
      margin-left: 110px;
      padding-top: 10px;
    }

    .viewbtn:hover {
      background-color: white;
      color: black;
      border: 1px solid black;
    }


    .profile-img {
      width: 70px;
      height: 70px;
      object-fit: cover;

      border-radius: 50%;
      margin-left: 10px;
    }


    .form-control:focus {

      box-shadow: none;
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        display: none;
        /* Adjusted to include display: none */
      }

      .navbar {
        margin-left: 0;
      }

      .main-content {
        margin-left: 0;
      }

      .navbar-toggler {
        display: none;
      }

      .navbar-light .navbar-brand {
        padding-left: 80px;
      }

    }

    /* Footer Menu */
    .footer-menu {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-around;
      background-color: #f1f8ff;
      padding: 10px 0;
      z-index: 1000;
    }

    .footer-menu a,
    .footer-menu .settings-link {
      color: #000000;
      font-size: 24px;
      text-align: center;
      text-decoration: none;
      position: relative;
    }

    .footer-menu a.active i,
    .footer-menu .settings-link.active i {
      color: #020202;
    }

    .footer-menu .settings-link .submenu {
      display: none;
      position: absolute;
      bottom: 65px;
      left: -250px;
      background-color: #ffffff;
      border: 1px solid #000000;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
      padding: 10px;
      z-index: 1001;
      white-space: nowrap;
    }

    .footer-menu .settings-link a {
      display: block;
      color: #000000;
      padding: 5px 10px;
      text-decoration: none;
      text-align: start;
      margin-top: 10px;
    }

    .footer-menu .settings-link a:hover {
      background-color: #000000;
      color: white;
      border-radius: 3px;
    }

    .footer-menu .settings-link.active {
      display: block;
    }

    .submenu {
      display: none;
      position: absolute;
      background-color: #fff;
      box-shadow: 0 0 10px rgb(192, 221, 253);
      z-index: 1000;
      left: 0;
      margin-top: 5px;
      padding: 10px;
      height: auto;
      width: 310px;
    }

    .sub-submenu {
      display: none;
      position: absolute;
      left: 100%;
      /* Position sub-submenu to the right of submenu */
      top: 0;
      margin-top: -10px;
      /* Adjust top margin as needed */
      padding: 5px 0;
      /* Add padding to sub-submenu */
      background-color: #f9f9f9;
      /* Adjust background color */
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
      /* Optional: Add shadow for better visibility */
    }

    .settings-link:hover .submenu,
    .settings-link:focus .submenu {
      display: block;
    }

    .sub-submenu {
      display: none;
    }

    .submenu-item:hover .sub-submenu,
    .submenu-item:focus .sub-submenu {
      display: block;
    }

    .btn-success {
      background-color: white;
      color: #000000;
    }

    .btn-success:hover {
      background-color: green;
      color: white;
    }

    .btn-danger {
      background-color: white;
      color: rgb(0, 0, 0);
    }

    .btn-danger:hover {
      background-color: rgb(230, 17, 17);
      color: rgb(255, 255, 255);
    }

    .btn-link {
      color: #000000;
      padding-left: 2px;
      margin-bottom: -12px;
    }

    .btn-link:hover {
      color: #000000;
      text-decoration: none;
    }

    .submenu a {
      display: block;
      padding: 8px 12px;
      text-decoration: none;
      color: #333;
      /* Example text color */
    }

    .submenu a:hover {
      background-color: #f0f0f0;
      /* Example hover background color */
    }

    .container {
      background-color: #fff;
      /* Light background for container */
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    h1 {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-weight: 700;
      color: #343a40;
      /* Dark grey text */
    }

    .text-muted {
      color: #ccc;
      /* Light grey text for meta info */
    }

    .btn-secondary,
    .btn-warning,
    .btn-danger {
      background-color: #6c757d;
      /* Grey button */
      border-color: #6c757d;
    }

    .btn-secondary:hover,
    .btn-warning:hover,
    .btn-danger:hover {
      background-color: #5a6268;
      /* Dark grey hover */
      border-color: #5a6268;
    }

    .icon-text-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .icon-text-wrapper i {
      font-size: 24px;
      /* Adjust the icon size as needed */
      margin-bottom: 5px;
      /* Space between icon and text */
    }

    .dashboard-text {
      font-size: 12px;
      /* Match the font size to the icon size */
      line-height: 24px;
      /* Set the line-height to match the icon's height */
    }
  </style>
</head>

<body>


  <!-- Sidebar -->
  {% include "sidebar.html" %}

  <!-- Dashboard content -->


  <div class="container mt-5">
    <h1 class="text-center">{{ blog.title }}</h1>
    <p class="text-muted text-center">{{ blog.category }} | {{ blog.date_created }}</p>
    <img src="{{ blog.image.url }}" class="img-fluid rounded mx-auto d-block" alt="{{ blog.title }}" width="300"
      height="200" loading="lazy">
    <div class="mt-4">
      <p>{{ blog.description }}</p>
      <div>{{ blog.content|safe }}</div>
    </div>
    <div class="text-center mt-4">
      <a href="/blogs/" class="btn btn-secondary">Back to Blog List</a>
      {% comment %} <a href="/blogs/update/{{blog.pk}}/" class="btn btn-warning">Edit</a>
      <a href="/blogs/delete/{{blog.pk}}/" class="btn btn-danger">Delete</a> {% endcomment %}
    </div>
  </div>
  </div>



  <!-- JavaScript dependencies -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

  <script>
    // Get the menu icon and submenu elements
    const menuIcon = document.getElementById('menu-icon');
    const submenu = document.getElementById('submenu');

    // Add click event listener to the menu icon
    menuIcon.addEventListener('click', function () {
      submenu.classList.toggle('show');
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      var menuIcon = document.getElementById('menu-icon');
      var submenu = document.getElementById('submenu');

      // Toggle submenu visibility on menu icon click
      menuIcon.addEventListener('click', function () {
        if (submenu.style.display === 'block') {
          submenu.style.display = 'none';
        } else {
          submenu.style.display = 'block';
        }
      });
    });

  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      var footerSearch = document.getElementById('footer-search');
      var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

      footerSearch.addEventListener('click', function () {
        exampleModal.show();
      });
    });
  </script>
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

</body>

</html>