    <!-- base template -->

    {% include "baseTemplate.html" %}
    
    {% comment %} Palns {% endcomment %}
    {% if plans %}
    <section id="pricing" class="pricing section-bg">
        <div class="container">
            <div class="section-title mt-3" data-aos="fade-up">
                <h2>Pricing</h2>
                <p>"An affordable CRM tool for libraries and study centers, streamlining membership, and scheduling.
                    Easy-to-use and insightful, it enhances organization and efficiency, fostering an engaging
                    management"</p>
                <div class="row mt-3 pricing-row">
                    {% for plan in plans %}
                    <div class="col-md-4">
                        <div class="box" data-aos="zoom-in" data-aos-delay="100">
                            <!-- Ribbon -->
                            <div class="ribbon">
                                <div>
                                    SALE <i class="fa-solid fa-tags"></i>
                                </div>
                            </div>

                            {% if plan.recommended %}
                            <div class="ribbon">
                                <div>
                                    RECOMMANDED 
                                </div>
                            </div>
                            {% endif %}
                            <h2>{{ plan.name }}</h2>

                            <h5>
                                <small style="font-size: 1rem;"> ₹<del>{{ plan.price }}</del>  </small>
                        
                                <span style="font-weight: 700;"> {{ plan.discount_price }} </span> <br/>
                                
                                {{plan.duration_months}} month
                             
                            </h5>
                                                
                            <ul class="mt-4">
                                <li>{{ plan.description_line_01 }}</li>
                                <li>{{ plan.description_line_02 }}</li>
                                <li>{{ plan.description_line_03 }}</li>
                                <li>{{ plan.description_line_04 }}</li>
                                <li>{{ plan.description_line_05 }}</li>

                                <!-- <li class="na">{{ plan.description_line_05 }}</li> -->
                            </ul>
                            <div class="btn-wrap">
                                <a href="/membership/pre-package/{{plan.id}}" class="btn-buy">Buy Now</a>

                                <!-- <a href="/membership/create_order/{{plan.id}}" class="btn-buy">Buy Now</a> -->
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    {% comment %} SMS Plans {% endcomment %}
    {% if smsplans %}
    <section id="pricing" class="pricing section-bg">
        <div class="container">
            <div class="section-title mt-3" data-aos="fade-up">
                <h2>Pricing</h2>
                <p>"An affordable CRM tool for libraries and study centers, streamlining membership, and scheduling.
                    Easy-to-use and insightful, it enhances organization and efficiency, fostering an engaging
                    management"</p>
                <div class="row mt-3 pricing-row">
                    {% for plan in smsplans %}
                    <div class="col-md-4 mb-4">
                        <div class="box" data-aos="zoom-in" data-aos-delay="100">
                            <!-- Ribbon -->
                            <div class="ribbon">
                                <div>
                                    SALE <i class="fa-solid fa-tags"></i>
                                </div>
                            </div>

                            {% if plan.recommended %}
                            <div class="ribbon">
                                <div>
                                    RECOMMANDED 
                                </div>
                            </div>
                            {% endif %}
                           
                            <!-- {% if plan.recommended %}
                            <span class="recommended-badge">Recommended</span>
                            {% endif %} -->
                            <h2 class="mt-2">{{ plan.name }}</h2>
                            <h4>
                                  <small style="font-size: 1rem;"> ₹ <s> <span>  {{ plan.price }} </span> </s> </small>
                                {{ plan.discount_price }} /  {{plan.duration_months}} month
                            </h4>
                           
                          
                            <ul>
                                <li>{{ plan.description_line_01 }}</li>
                                <li>{{ plan.description_line_02 }}</li>
                                <li>{{ plan.description_line_03 }}</li>
                                <!-- <li class="na">{{ plan.description_line_04 }}</li>
                                <li class="na">{{ plan.description_line_05 }}</li>  -->
                            </ul>
                            <div class="btn-wrap">
                                <a href="/membership/create_sms_order/{{plan.id}}" class="btn-buy">Buy Now</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>
    {% endif %}
    <!-- Add this container for the button -->
    <div class="d-flex justify-content-center mb-5">
        <a href="/librarian/dashboard/" class="btn btn-outline-success">Back to Dashboard</a>
    </div>


    <script>
         
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"
        async></script>

</body>

</html>