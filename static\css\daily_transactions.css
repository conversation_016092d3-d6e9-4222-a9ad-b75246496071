body {
    -webkit-user-select: none;
    /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
}


.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

.card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
}

.table {
    text-align: left !important;
}


.card_heading{
    font-size: 1.5em;
}

.card-header {
    background: #294282 !important;
    /* background: linear-gradient(283deg, rgba(0, 185, 215, 0.9416141456582633) 0%, rgba(0, 49, 153, 0.8883928571428571) 100%); */
    color: #fff;
    text-align: center;
    border-top-left-radius: 1rem !important;
    border-top-right-radius: 1rem !important;
    border-radius: 1rem;
    padding: 20px;
    letter-spacing: 1px;
}
.table_fansy_row {
    background: #cee3e0 !important;
    color: #fff;
    text-align: center;
    padding: 20px;
    /* font-size: 1.5em; */
    letter-spacing: 1px;
}

.alert {
    margin-bottom: 15px;
}

.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;

}

@media (max-width:768px) {
    .footer {
        margin-bottom: 100px;
    }

    .footer img {
        width: 60%;
    }

    .card_heading{
        font-size: 1rem;
    }

}

