<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

  <!-- Primary Meta Tags -->
  <title>{{library.library_name}} | Library Details, Hours & Contact | Librainian</title>
  <meta name="robots" content="index, follow, max-image-preview:large">
  <meta name="description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. Register easily as a student with our convenient QR code system. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %} Find the perfect environment for studying, research, and academic excellence.">
  <meta http-equiv="content-language" content="en">
  <meta name="keywords" content="{{library.library_name}}, best library in {{library.librarian_address}}, study space near {{library.librarian_address}}, {{library.library_name}} the quiet reading room, {{library.library_name}}'s opening hours, {{library.library_name}}'s contact information, {{library.library_name}}'s online registration form, {{library.library_name}} registration,{{library.library_name}} uses Librainian app, {{library.library_name}} location map, {{library.library_name}} contact number, {{library.library_name}} working hours, {{library.library_name}} timings,  library near me, top rated libraries in {{library.librarian_address}}, academic library, public library services, digital library access, library facilities, library amenities, library seating capacity, library membership fees,{{library.library_name}} for best at student services, {{library.library_name}} for exam preparation space, {{library.library_name}} reviews, {{library.library_name}} ratings{% if library.discount_available %}, find your library with librainian to get special library discount, librainian special library offers, Librainian discounted library membership, {{library.discount_amount}}% off library services, student discount in {{library.librarian_address}}{% endif %}">
  <meta name="geo.region" content="IN">
  <meta name="author" content="Librainian">
  <link rel="canonical" href="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Librainian">
  <meta property="og:title" content="{{library.library_name}} | Library Details & Information">
  <meta property="og:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available for a limited time!{% endif %}">
  <meta property="og:url" content="https://www.librainian.com/librarian/library-details/{{ library.slug }}/">
  <meta property="og:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@librainian_app">
  <meta name="twitter:creator" content="@librainian_app">
  <meta name="twitter:title" content="{{library.library_name}} | Library Details & Information">
  <meta name="twitter:description" content="Discover {{library.library_name}}, a premier library in {{library.librarian_address}} offering excellent study spaces and reading facilities. View detailed information about opening hours, contact details, and available services. {% if library.discount_available %}Special student discount of {{library.discount_amount}}% available!{% endif %}">
  <meta name="twitter:image" content="{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}">

<!-- Structured Data - Library Information -->
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ library.library_name }}",
    "image": "{% if library.image %}{{ library.image.url }}{% else %}https://www.librainian.com/static/img/library-default.jpg{% endif %}",
    "description": "{{ library.description|escapejs }}",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "{{ library.librarian_address }}",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 20.5937,
      "longitude": 78.9629
    },
    "url": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/",
    "telephone": "{{ library.librarian_phone_num }}",
    "email": "{{ library.user.email }}",
    "openingHoursSpecification": [
      {% for shift in shifts %}
      {
        "@type": "OpeningHoursSpecification",
        "name": "{{ shift.name }}",
        "dayOfWeek": [
          "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
        ],
        "opens": "{{ shift.opens }}",
        "closes": "{{ shift.closes }}",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "{{ shift.rate }}",
          "priceCurrency": "INR",
          "description": "Fee for {{ shift.name }} shift"
        }
      }{% if not forloop.last %},{% endif %}
      {% endfor %}
    ],
    "sameAs": [
      "https://www.librainian.com/",
      "https://www.facebook.com/librainian",
      "https://twitter.com/librainian_app"
    ]
  }
  </script>
  

  <!-- Breadcrumb Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.librainian.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Libraries",
        "item": "https://www.librainian.com/librarian/library-list/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "{{library.library_name}}",
        "item": "https://www.librainian.com/librarian/library-details/{{ library.slug }}/"
      }
    ]
  }
  </script>

  <!-- Contact Form Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact {{library.library_name}}",
    "description": "Contact form to reach out to {{library.library_name}} for inquiries and information",
    "mainEntity": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "telephone": "{{library.librarian_phone_num}}",
      "email": "{{library.user.email}}",
      "areaServed": "{{library.librarian_address}}",
      "availableLanguage": ["English", "Hindi"]
    }
  }
  </script>


  <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <style>
       body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
        }

    .qr_body {
            margin: 0.5rem 0rem 2rem 0rem;
            border-radius: 1rem;
            background: linear-gradient(135deg, #28a745, #a3e0dcfc);
            color: #ffffff;
        }
        .qr-code-container {
            background-color: #ffff;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .qr-card {
            border-radius: 1rem;
            padding: 20px;
            text-align: center;
            width: 100%;
            background: #ffffff;
            color: #1e293b;
        }
        .qr-card h1 {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .qr-card img {
            border: 5px solid #294282;
            border-radius: 10px;
            margin-top: 20px;
            max-width: 100%;
            height: auto;
        }
        .qr-card p {
            font-size: 1rem;
            margin-top: 15px;
            color: #6b7280;
        }

        .btn_theme{
            color: #fff;
            background-color: #294282;
        }
        .btn_theme:hover{
            color: #fff;
            background-color: #28345a;
        }

        @media (max-width: 768px) {
      .contact_box{
        background-color: #fff;
        padding: 20px;
      }
    }
  </style>
</head>

<body>

  <div class="container-fluid p-0">
    <div class="card shadow-sm border-0" style="background-color: #9bc6bf; padding: 20px;">
        <div class="card-body text-center d-flex flex-column align-items-center">
            <h1 class="card-title text-center" style="font-size: 36px; font-weight: 600; color: #2c3e50;">{{library.library_name}}</h1>
            <p class="lead text-dark mb-0">Premier Library in {{library.librarian_address}}</p>
            {% if library.discount_available %}
            <div class="badge bg-warning text-dark mt-2 p-2" style="font-size: 16px;">
                <i class="fa-solid fa-tags me-1"></i> Special Discount: {{library.discount_amount}}% Off
            </div>
            {% endif %}
        </div>

        <div class="row mx-auto mt-4 w-100 mb-md-4">
            <div class="col-md-4 mb-md-0 mb-5 bg-white p-4" style="border-radius: 1rem;">
                <h2 style="font-size: 24px; color: #2c3e50; margin-bottom: 20px;">Library Information</h2>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-location-dot mt-1" style="color: #e74c3c; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">Address</h3>
                        <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">{{library.librarian_address}}</p>
                    </div>
                </div>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-envelope-square mt-1" style="color: #3498db; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">Email</h3>
                        <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">
                            <span id="emailText">{{library.user.email}}</span>
                            <i class="fa-solid fa-copy ms-2" style="color: #2c3e50; cursor: pointer;" onclick="copyToClipboard('emailText')" title="Copy email"></i>
                            <i class="fa-solid fa-envelope-open ms-2" style="color: #3498db; cursor: pointer;" onclick="window.location.href = 'mailto:{{library.user.email}}'" title="Send email"></i>
                        </p>
                    </div>
                </div>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-phone-square mt-1" style="color: #3498db; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">Contact</h3>
                        <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">
                            <span id="phoneText">{{library.librarian_phone_num}}</span>
                            <i class="fa-solid fa-copy ms-2" style="color: #2c3e50; cursor: pointer;" onclick="copyToClipboard('phoneText')" title="Copy phone number"></i>
                            <i class="fa-solid fa-phone ms-2" style="color: #3498db; cursor: pointer;" onclick="window.location.href = 'tel:{{library.librarian_phone_num}}'" title="Call library"></i>
                        </p>
                    </div>
                </div>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-clock mt-1" style="color: #f39c12; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">Hours & Pricing</h3>
                        <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">
                            <span class="badge bg-success me-2">Currently Open</span><br>
                            {% for shift in shifts %}
                            <span class="d-block mt-2">{{shift.name}} - ₹{{shift.price}}</span>
                            {% endfor %}
                        </p>
                    </div>
                </div>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-circle-info mt-1" style="color: #8e44ad; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">About This Library</h3>
                        <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">{{library.description}}</p>
                    </div>
                </div>

                <div class="d-flex align-items-start mb-3">
                    <i class="fa-solid fa-map-location-dot mt-1" style="color: #27ae60; font-size: 20px; width: 30px;"></i>
                    <div>
                        <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 5px;">Location</h3>
                        <a href="{{library.google_map_url}}" target="_blank" class="btn btn-sm btn-outline-primary mt-1" style="border-radius: 20px;">
                            <i class="fa-solid fa-map-marker-alt me-1"></i> View on Google Maps
                        </a>
                    </div>
                </div>

                {% if library.discount_available %}
                <div class="mt-4 p-3 bg-warning bg-opacity-10 rounded-3">
                    <h3 style="font-size: 18px; color: #2c3e50; margin-bottom: 10px;">
                        <i class="fa-solid fa-tags me-2" style="color: #f39c12;"></i> Special Offer
                    </h3>
                    <p style="font-size: 16px; color: #34495e; margin-bottom: 0;">
                        Enjoy a {{library.discount_amount}}% discount on all services. Limited time offer for students!
                    </p>
                </div>
                {% endif %}
            </div>

            <div class="col-md-4 mb-md-0 mb-5 contact_box" style="border-radius: 1rem;">
                {% if success_message %}
                <div class="alert alert-success" role="alert">
                    {{ success_message }}
                </div>
                {% endif %}
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    {{ error_message }}
                </div>
                {% endif %}
                <form class="contact-form p-4" method="POST" style="background-color: #ffffff; border-radius: 1rem;">
                    {% csrf_token %}
                    <h2 class="mb-3" style="font-weight: 600; color: #2c3e50;">Contact {{ library.library_name }}</h2>
                    <p class="text-muted mb-4">Have questions about our services, hours, or facilities? Send us a message and we'll get back to you shortly.</p>

                    <div class="form-group mb-3">
                        <label for="name" style="font-size: 16px; color: #34495e;">Your Name</label>
                        <input type="text" class="form-control" name="name" id="name" placeholder="Enter your full name" required style="border-radius: 8px; padding: 10px;">
                    </div>

                    <div class="form-group mb-3">
                        <label for="email" style="font-size: 16px; color: #34495e;">Email Address</label>
                        <input type="email" class="form-control" name="email" id="email" placeholder="Enter your email address" required style="border-radius: 8px; padding: 10px;">
                    </div>

                    <div class="form-group mb-4">
                        <label for="message" style="font-size: 16px; color: #34495e;">Your Message</label>
                        <textarea class="form-control" name="message" id="message" rows="3" placeholder="What would you like to know about our library?" required style="border-radius: 8px; padding: 10px;"></textarea>
                    </div>

                    <button type="submit" class="btn btn_theme" style="padding: 8px 16px; font-size: 16px; border-radius: 1rem; font-weight: 500; margin-right: 10px;">
                        <i class="fas fa-paper-plane me-2"></i> Send Message
                    </button>
                </form>
            </div>
            <!-- for the QR -->
            <div class="col-md-4 mb-md-0 mb-5 bg-white" style="border-radius: 1rem;">
                <div class="container-fluid p-0 qr-code-container">
                    <div class="qr-card">
                        <h2>QR Code for Student Registration</h2>
                        <p class="mb-3">Scan this QR code to register as a student at {{ library.library_name }}</p>
                        <img id="qrImage" src="data:image/png;base64,{{ qr_code }}" alt="QR Code for {{ library.library_name }} student registration" class="img-fluid">
                        <p class="mt-3">This QR code provides quick access to our registration system</p>
                        <button id="downloadQR" class="btn btn_theme" style="border-radius: 1rem; padding: 8px 16px;" onclick="downloadQR()">
                            <i class="fas fa-download me-2"></i> Download QR Code
                        </button>
                    </div>
                </div>
            </div>
<!-- QR end                -->


        </div>

        <div class="d-flex justify-content-center mb-5">
            <a href="/librarian/library-list/" class="btn btn-secondary" style="padding: 8px 16px; font-size: 16px; border-radius: 1rem; font-weight: 500;">Back to Library List</a>
        </div>
    </div>

</div>




  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
    // Loader functionality
    $(document).ready(function() {
        $('#loader').show(); // Show the loader on page load

        $(window).on('load', function() {
            $('#loader').hide();
        });

        $('.contact-form').on('submit', function() {
            $('#loader').show();

            // Track form submission event
            gtag('event', 'form_submission', {
              'event_category': 'Contact',
              'event_label': '{{library.library_name}} Contact Form'
            });
        });

        // Track QR code download
        $('#downloadQR').on('click', function() {
            gtag('event', 'qr_download', {
              'event_category': 'Engagement',
              'event_label': '{{library.library_name}} QR Code'
            });
        });
    });
  </script>
  <script>
        function downloadQR() {
            const qrImage = document.getElementById('qrImage');
            const link = document.createElement('a');
            link.href = qrImage.src;
            link.download = 'qr_code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>

<script>
    // Function to copy content to clipboard and show an alert with a custom message
    function copyToClipboard(elementId, type) {
        const text = document.getElementById(elementId).textContent || document.getElementById(elementId).innerText;
        navigator.clipboard.writeText(text).then(() => {
            // Show an alert with the specific message based on the copied type
            alert('Copied to clipboard!');

        }).catch(err => {
            console.error('Failed to copy text: ', err);
        });
    }
</script>
<script>
    window.addEventListener("load", function () {
        const path = window.location.pathname; // Get current page path
        let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

        // Increment count for the current path
        pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

        // Store updated data back to localStorage
        localStorage.setItem("page_data", JSON.stringify(pageData));
      });

      // Function to send page view data
      function sendPageData() {
        const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

        if (Object.keys(pageData).length > 0) {
          fetch(location.origin + "/librarian/track-page-view/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": "{{ csrf_token }}",
            },
            body: JSON.stringify(pageData),
          })

            .then(() => {
              localStorage.removeItem("page_data");
            })
            .catch((error) => console.error("Error sending page data:", error));
            localStorage.removeItem("page_data");
        } else {

          console.log("No page data to send");
        }
      }

      // Send data every 10 seconds
      setInterval(sendPageData, 10000);
</script>
</body>

</html>
