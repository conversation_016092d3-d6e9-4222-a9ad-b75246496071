#!/bin/bash

# Log files
ACCESS_LOG="/var/log/nginx/access.log"
ERROR_LOG="/var/log/nginx/error.log"

# Output files
ACCESS_OUTPUT="/one/lms/system_logs/access_log.txt"
ERROR_OUTPUT="/one/lms/system_logs/error_log.txt"

# Run tail for both logs in separate files
tail -f "$ACCESS_LOG" >> "$ACCESS_OUTPUT" &
tail -f "$ERROR_LOG" >> "$ERROR_OUTPUT" &

# Wait for both background processes to complete
wait
