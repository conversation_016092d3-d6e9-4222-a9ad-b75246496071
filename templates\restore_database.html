<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Restoration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a4b7c;
            --secondary-color: #2c3e50;
            --accent-color: #4a90e2;
            --background-color: #f8f9fa;
            --text-color: #2c3e50;
            --border-radius: 12px;
        }

        * {
            transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .restore-container {
            max-width: 560px;
            width: 100%;
            margin: auto;
            padding: 2rem 15px;
        }

        .restore-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            background: white;
        }

        .card-header {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-bottom: none;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
        }

        .card-header h3 {
            margin: 0;
            font-weight: 700;
            letter-spacing: 1px;
            position: relative;
            z-index: 1;
        }

        .card-body {
            background-color: white;
            padding: 2.5rem;
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .form-label i {
            margin-right: 0.5rem;
            color: var(--accent-color);
        }

        .form-control {
            border: 1px solid #e1e6eb;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 8px;
            padding: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.2);
            transform: skewX(-45deg);
            transition: all 0.5s;
            z-index: -1;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 14px rgba(0,0,0,0.1);
        }

        .back-btn {
            background: var(--secondary-color);
            color: white;
            border-radius: 8px;
            padding: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-align: center;
            display: block;
            text-decoration: none;
            font-weight: 600;
            margin-top: 1rem;
        }

        .back-btn:hover {
            background: var(--primary-color);
            transform: translateY(-3px);
        }

        .alert {
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        footer {
            text-align: center;
            padding: 1.5rem;
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: auto;
        }

        @media (max-width: 576px) {
            .restore-container {
                padding: 1rem;
            }
            .card-body {
                padding: 2rem 1.5rem;
            }
        }

        /* Custom file input styling */
        .custom-file-input {
            position: relative;
            overflow: hidden;
        }

        .custom-file-input input[type=file] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            background: white;
            cursor: inherit;
            display: block;
        }

        .custom-file-input .file-select-name {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="restore-container">
        <div class="restore-card card">
            <div class="card-header">
                <h3>Database Restoration</h3>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <label for="db_name" class="form-label">
                            Database Name
                        </label>
                        <input type="text" class="form-control" id="db_name" name="db_name" required placeholder="Enter database name">
                    </div>

                    <div class="form-group mb-3">
                        <label for="db_user" class="form-label">
                            Database User
                        </label>
                        <input type="text" class="form-control" id="db_user" name="db_user" required placeholder="Enter database user">
                    </div>

                    <div class="form-group mb-3">
                        <label for="db_password" class="form-label">
                            Database Password
                        </label>
                        <input type="password" class="form-control" id="db_password" name="db_password" required placeholder="Enter database password">
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-8 form-group">
                            <label for="db_host" class="form-label">
                                Database Host
                            </label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" placeholder="Enter host">
                        </div>
                        <div class="col-md-4 form-group">
                            <label for="db_port" class="form-label">
                                Port
                            </label>
                            <input type="number" class="form-control" id="db_port" name="db_port" value="5432" placeholder="Port">
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        <label for="backup_file" class="form-label">
                            Backup File
                        </label>
                        <div class="custom-file-input">
                            <input type="file" class="form-control" id="backup_file" name="backup_file" required>
                            <div class="file-select-name">Choose backup file...</div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        Restore Database
                    </button>
                </form>
                
                <a href="/{{role}}/dashboard" class="back-btn">
                    Back to Dashboard
                </a>
            </div>
        </div>

        {% if messages %}
        <div class="mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} p-3 rounded">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <footer>
        <p>&copy; {{ current_year }} Librainian | Database Management System</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Custom file input handling
        document.querySelectorAll('.custom-file-input input[type="file"]').forEach(function(input) {
            input.addEventListener('change', function() {
                var fileName = this.value.split('\\').pop();
                var nameContainer = this.closest('.custom-file-input').querySelector('.file-select-name');
                if (fileName) {
                    nameContainer.textContent = fileName;
                } else {
                    nameContainer.textContent = 'Choose backup file...';
                }
            });
        });
    </script>
</body>
</html>