<!DOCTYPE html>
<html lang="en">
<head>
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Success</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts - Inter for modern look -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for better icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Global Loader Script -->

    <!-- Custom styles -->
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-dark: #059669;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-light: #f9fafb;
            --bg-white: #ffffff;
            --border-light: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        /* Main Container */
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        /* Floating particles background */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Success Card */
        .success-card {
            background: var(--bg-white);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            padding: 3rem 2rem;
            max-width: 600px;
            width: 100%;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Success Icon */
        .success-icon-container {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--success-color), var(--success-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: var(--shadow-lg);
            animation: successPulse 2s ease-in-out infinite;
        }

        .success-icon {
            font-size: 3rem;
            color: white;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Typography */
        .success-title {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
            text-align: center;
            line-height: 1.2;
        }

        .success-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .registration-id {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            margin: 1.5rem auto;
            display: inline-block;
            box-shadow: var(--shadow-md);
        }
        /* Feedback Section */
        .feedback-section {
            background: var(--bg-light);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 3rem;
            border: 1px solid var(--border-light);
        }

        .feedback-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .feedback-subtitle {
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: 2rem;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-light);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .form-range {
            margin: 1rem 0;
        }

        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        }

        .home-button {
            background: linear-gradient(135deg, var(--success-color), var(--success-dark));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 16px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .home-button:hover {
            color: white;
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            background: linear-gradient(135deg, var(--success-dark), var(--success-color));
        }
        /* Thank You Message */
        .thank-you-message {
            text-align: center;
            padding: 2rem;
        }

        .thank-you-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--success-color), var(--success-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            animation: successPulse 2s ease-in-out infinite;
        }

        .thank-you-icon i {
            font-size: 2rem;
            color: white;
        }

        /* Action Buttons Container */
        .action-buttons {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-light);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0.5rem;
            }

            .success-card {
                padding: 2rem 1.5rem;
                border-radius: 20px;
            }

            .success-icon-container {
                width: 100px;
                height: 100px;
            }

            .success-icon {
                font-size: 2.5rem;
            }

            .success-title {
                font-size: 1.75rem;
            }

            .success-subtitle {
                font-size: 1rem;
            }

            .feedback-section {
                padding: 1.5rem;
                margin-top: 2rem;
            }

            .home-button {
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .success-card {
                padding: 1.5rem 1rem;
            }

            .success-title {
                font-size: 1.5rem;
            }

            .feedback-section {
                padding: 1rem;
            }

            .form-control, .form-select {
                padding: 0.625rem 0.875rem;
            }
        }

        /* Animation for page load */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-card {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <!-- Floating particles background -->
    <div class="particles">
        <div class="particle" style="left: 10%; top: 20%; width: 10px; height: 10px; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; top: 80%; width: 8px; height: 8px; animation-delay: 1s;"></div>
        <div class="particle" style="left: 60%; top: 30%; width: 12px; height: 12px; animation-delay: 2s;"></div>
        <div class="particle" style="left: 80%; top: 70%; width: 6px; height: 6px; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; top: 10%; width: 14px; height: 14px; animation-delay: 4s;"></div>
        <div class="particle" style="left: 70%; top: 90%; width: 9px; height: 9px; animation-delay: 5s;"></div>
    </div>

    <div class="main-container">
        <div class="success-card">
            <!-- Success Icon -->
            <div class="success-icon-container">
                <i class="fas fa-check success-icon"></i>
            </div>

            <!-- Success Message -->
            <h1 class="success-title">
                {% if student.name %}
                    Congratulations, {{ student.name }}!
                {% else %}
                    Registration Successful!
                {% endif %}
            </h1>

            <p class="success-subtitle">
                Your registration has been submitted successfully via QR Code.
                {% if student.unique_id %}
                    <br><br>
                    <span class="registration-id">
                        <i class="fas fa-id-card me-2"></i>
                        Registration ID: {{ student.unique_id }}
                    </span>
                {% endif %}
            </p>

            <!-- Feedback Section -->
            <div class="feedback-section" id="feedbackFormContainer">
                {% if feedback_submitted %}
                    <!-- Thank you message -->
                    <div id="thankYouMessage" class="thank-you-message">
                        <div class="thank-you-icon">
                            <i class="fas fa-thumbs-up"></i>
                        </div>
                        <h4 class="feedback-title">Thank You for Your Feedback!</h4>
                        <p class="feedback-subtitle">Your input is valuable to us and will help us improve our services.</p>
                    </div>
                {% else %}
                    <!-- Feedback Form -->
                    <div id="feedbackFormContent">
                        <h4 class="feedback-title">Help Us Improve</h4>
                        <p class="feedback-subtitle">We're working hard to provide the best experience. Your feedback helps us grow!</p>

                            <form method="post" action="#" class="needs-validation" novalidate id="feedbackForm">
                                {% csrf_token %}

                                <!-- Hidden fields to store student information -->
                                {% if student %}
                                <input type="hidden" name="name" value="{{ student.name }}">
                                <input type="hidden" name="email" value="{{ student.email }}">
                                <input type="hidden" name="phone" value="{{ student.mobile }}">
                                {% if student.librarian %}
                                <input type="hidden" name="library" value="{{ student.librarian.id }}">
                                {% endif %}
                                {% endif %}

                                <input type="hidden" name="subject" value="Registration Feedback">

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        How fast was the form submission process?
                                    </label>
                                    <div class="d-flex justify-content-between mb-2 text-sm text-secondary">
                                        <span>Very Slow</span>
                                        <span>Very Fast</span>
                                    </div>
                                    <input type="range" class="form-range" min="1" max="5" id="submissionSpeed" name="submissionSpeed" value="3">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Did you get all the information in the correct manner?
                                    </label>
                                    <select class="form-select" id="informationCorrectness" name="informationCorrectness" required>
                                        <option value="" selected disabled>Please select an option</option>
                                        <option value="yes">✅ Yes, everything was clear</option>
                                        <option value="mostly">👍 Mostly, with minor issues</option>
                                        <option value="partially">⚠️ Partially, some information was unclear</option>
                                        <option value="no">❌ No, the information was confusing</option>
                                    </select>
                                    <div class="invalid-feedback">Please select an option.</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-palette me-2"></i>
                                        How was the look and feel of the application?
                                    </label>
                                    <div class="d-flex justify-content-between mb-2 text-sm text-secondary">
                                        <span>Poor</span>
                                        <span>Excellent</span>
                                    </div>
                                    <input type="range" class="form-range" min="1" max="5" id="lookAndFeel" name="lookAndFeel" value="3">
                                </div>

                                <div class="form-group">
                                    <label for="message" class="form-label">
                                        <i class="fas fa-comment-dots me-2"></i>
                                        Additional Feedback
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="4" placeholder="Share your thoughts, suggestions, or any other feedback..."></textarea>
                                </div>

                                <!-- Hidden field for rating -->
                                <input type="hidden" name="rating" id="rating" value="5">

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Submit Feedback
                                    </button>
                                </div>
                            </form>
                        </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="/" class="home-button">
                        <i class="fas fa-home"></i>
                        Explore App Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Form Validation Script -->
    <script>
    // Form validation
    (function() {
        'use strict';

        // Fetch all forms to which we want to apply validation
        var forms = document.querySelectorAll('.needs-validation');

        // Loop over them and prevent submission
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    form.classList.add('was-validated');
                } else {
                    // Set the rating based on lookAndFeel value
                    document.getElementById('rating').value = document.getElementById('lookAndFeel').value;

                    // Continue with form submission
                    // The server will handle the redirect
                }
            }, false);
        });

        // Update rating value when lookAndFeel slider changes
        document.getElementById('lookAndFeel').addEventListener('input', function() {
            document.getElementById('rating').value = this.value;
        });

        // Handle feedback form submission
        const feedbackForm = document.getElementById('feedbackForm');
        if (feedbackForm) {
            feedbackForm.addEventListener('submit', function(event) {
                event.preventDefault(); // Prevent actual form submission

                // Show thank you message with modern design
                const formContent = document.getElementById('feedbackFormContent');
                const thankYouMessage = `
                    <div id="thankYouMessage" class="thank-you-message">
                        <div class="thank-you-icon">
                            <i class="fas fa-thumbs-up"></i>
                        </div>
                        <h4 class="feedback-title">Thank You for Your Feedback!</h4>
                        <p class="feedback-subtitle">Your input is valuable to us and will help us improve our services.</p>
                    </div>
                `;

                if (formContent) {
                    formContent.innerHTML = thankYouMessage;
                }
            });
        }
    })();
    </script>
</body>
</html>
