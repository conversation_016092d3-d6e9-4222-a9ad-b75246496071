<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Librarian Membership Checkout</title>
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

       <!-- Disable Right click -->

         
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
  <style>
    body {
      -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
      background-color: #f4f6f9;
      color: #333;
    }
    .card {
      border-radius: 15px;
      border: 1px solid #ddd;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 20px;
    }
    .card-header {
      background-color: #007bff;
      color: white;
      padding: 1rem;
      border-bottom: 1px solid #0056b3;
    }
    .card-body {
      /* padding: 2rem; */
      display: flex;
      flex-direction: column;
    }
    .img-section {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      /* border-bottom: 1px solid #ddd; */
      /* padding-bottom: 20px; */
    }
    .img-section img {
      max-width: 150px;
      height: auto;
      margin-right: 20px;
      border-radius: 10px;
    }
    .info {
      flex: 1;
    }
    .price-section {
      text-align: right;
    }
    .price-section h5 {
      margin-bottom: 10px;
    }
    .discount {
      font-size: 0.9rem;
      color: #e63946;
    }
    .remove-btn {
      background-color: #ff6b6b;
      border: none;
      border-radius: 5px;
      color: white;
      padding: 10px 20px;
      cursor: pointer;
    }
    .remove-btn:hover {
      background-color: #e63946;
    }
    .pricing-details {
      margin-top: 20px;
    }
    .pricing-details .detail {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .place-order-btn {
      margin-top: 20px;
      text-align: center;
    }
    .place-order-btn button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1rem;
    }
    .place-order-btn button:hover {
      background-color: #0056b3;
    }
    .card-footer {
      background-color: #f1f1f1;
      padding: 1rem;
      border-top: 1px solid #ddd;
      text-align: center;
    }
    h6 {
      color: grey;
    }
    @media (max-width: 576px) {
      .img-section {
        flex-direction: column;
        align-items: flex-start;
      }
      .img-section img {
        margin-right: 0;
        margin-bottom: 10px;
      }
      .price-section {
        text-align: left;
      }
      .card-body {
        padding: 1rem;
      }
    }
    .list-group{
      padding-left: 20px;
    }
  </style>
</head>
<body>

  <div class="container mt-5">
    <div class="row">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h4>Library Membership Checkout</h4>
          </div>
          <div class="card-body">
            <div class="img-section">
              <div class="info">
                <h1>{{plan.name}}</h1>
                <ul class="list-group">
                <li>{{plan.description_line_01}}</li>
                <li>{{plan.description_line_02}}</li>
                <li>{{plan.description_line_03}}</li>

                </ul>
              </div>
              <div class="price-section">
                <h5>₹{{plan.price}}</h5>
                <div class="discount"><del>₹{{plan.price}}</del> 50% off</div>
                <button class="remove-btn"><a href="/membership/plans/" class="text-white">Remove</a></button>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <h6>Items in Cart: <strong>1</strong></h6>
            <h5>Total: ₹{{plan.price}}</h5>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <div class="pricing-details">
              <div class="detail">
                <h6>Total:</h6>
                <h6>₹{{plan.price}}</h6>
              </div>
              <div class="detail">
                <h6>App discount:</h6>
                <h6 style="color: green;">50%</h6>
              </div>
              <div class="detail">
                <h6>Tax <i class="fa-solid fa-circle-info"></i>:</h6>
                <h6>0</h6>
              </div>
           
              <div class="detail">
                <h6>Order Total:</h6>
                <h6>₹{{plan.price}}</h6>
              </div>
              <hr>
              <div class="detail">
                <h6>Grand Total:</h6>
                <h6>₹{{plan.price}}</h6>
              </div>
            </div>
            <div class="place-order-btn">
              <!-- Razorpay Button -->
              <form action="{% url 'sms_verify_payment' %}" method="post" id="razorpay-form">
                  {% csrf_token %}
                  <!-- Razorpay Checkout Button -->
                  <button id="pay-button" type="button">Checkout</button>

                  <!-- Hidden Fields to Store Payment Information -->
                  <input type="hidden" name="plan_id" value="{{ plan.id }}">
                  <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
                  <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
                  <input type="hidden" name="razorpay_signature" id="razorpay_signature">
              </form>
            </div>
          </div>
        </div>
        <div class="card p-2">
          <div class="detail">
            <h6>Tax <i class="fa-solid fa-circle-info"></i>: <span>Please note that our business turnover does not exceed the specified limit for GST registration, hence GST is not applicable to our products/services.</span>  </h6>
            
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  <script>
    document.getElementById('pay-button').onclick = function(e) {
      var options = {
          "key": "{{ razorpay_key }}", // Enter the Key ID generated from the Dashboard
          "amount": "{{ amount }}", // Amount in paisa
          "currency": "{{ currency }}",
          "name": "Library Membership",
          "description": "Membership Plan",
          "image": "https://example.com/your_logo.jpg",
          "order_id": "{{ order_id }}", // Pass the order_id generated in your server
          "handler": function (response) {
              // Populate hidden fields with the payment response
              document.getElementById('razorpay_order_id').value = response.razorpay_order_id;
              document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
              document.getElementById('razorpay_signature').value = response.razorpay_signature;

              // Automatically submit the form after payment success
              document.getElementById('razorpay-form').submit();
          },
          "prefill": {
              "name": "{{ user.username }}",
              "email": "{{ user.email }}"
          },
          "theme": {
              "color": "#007bff"
          }
      };
      var rzp1 = new Razorpay(options);
      rzp1.open();
      e.preventDefault();
    }
  </script> 

  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>