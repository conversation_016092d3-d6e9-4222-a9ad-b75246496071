    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar -->
     
    {% include "sidebar.html" %}

    <br>
        <div class="d-flex justify-content-center">
            <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
               LMS / DASHBOARD
            </div>
        </div>

        <button id="notificationBtn">Enable Notifications</button>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">Students This Month</h5>
                            <p class="card-text">{{students_count}}</p>
                            <div class="icon"><i class="fas fa-users"></i></div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Updated just now</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">Total Registrations</h5>
                            <p class="card-text">{{registration_count}}</p>
                            <div class="icon" style="font-size: 28px;"><i class="bi bi-currency-rupee"></i></div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Updated just now</small>
                        </div>
                    </div>
                </div>
                {%if role != "sublibrarian"%}
                <div class="col-lg-3 col-md-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">Sales This Month</h5>
                            <p class="card-text">{{total_invoice_amount}}</p>
                            <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Updated just now</small>
                        </div>
                    </div>
                </div>
                {%endif%}
                <div class="col-lg-3 col-md-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">Complaints</h5>
                            <p class="card-text">{{complaints_count}}</p>
                            <div class="icon"><i class="fas fa-comments"></i></div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Updated just now</small>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Charts -->
            {%if role != "sublibrarian"%}
            <div class="row">
                <div class="col-lg-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">Monthly Sales</h5>
                            <canvas id="monthlySalesChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card dashboard_card">
                        <div class="card-body">
                            <h5 class="card-title">User Growth</h5>
                            <canvas id="userGrowthChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            {%endif%}


            <!-- Loaction Table & Map -->

            
    <div class="row">
        {%if role != "sublibrarian"%}
            <div class="col-md-6">
                <div class="card dashboard_card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="userTable3" class="table table-striped table-bordered">
                                <thead class="center-text">
                                    <tr>
                                        <th scope="col">Area</th>
                                        <th scope="col">No. Of Students</th>
                                    </tr>
                                </thead>
                                <tbody class="center-text">
                                    {% for entry in state_wise_student_count %}
                                    <tr>
                                        <td>{{ entry.locality }}</td>
                                        <td>{{ entry.count }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {%endif%}
            <!-- <div class="col-md-6">
                <div class="card card-round">
                    <div class="card-body">
                        <h5 class="card-title">New Students</h5>
                        <div class="table-responsive">
                            <table id="userTable4" class="table table-striped table-bordered">
                                <thead class="center-text">
                                    <tr>
                                        <th scope="col">_</th>
                                        <th scope="col">Name</th>
                                        <th scope="col">Course</th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="center-text">
                                    {% for entry in students_data %}
                                    <tr>
                                        <td><i class="fas fa-user"></i></td>
                                        <td>{{ entry.name }}</td>
                                        <td>{{ entry.course }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-icon btn-outline-info op-8 me-1 enveloppe" data-slug="{{ entry.slug }}"><i class="far fa-envelope"></i></button>
                                                <button class="btn btn-icon btn-danger ban"><i class="fas fa-ban"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>


        <div class="modal fade" id="emailModal" tabindex="-1" role="dialog" aria-labelledby="emailModalLabel"
                    aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="emailModalLabel">Send Email</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                Are you sure you want to send an email?
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" id="sendMailBtn">Send Email</button>
                                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>


            <!-- footer -->
            <div class="footer">
                <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
                <!-- <p>Developed with passion by Librainian</p> -->
            </div>
        </div>
    </div>

    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
     
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>
    <script src="/static/js/push_notifications.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Function to generate a unique ID for each row based on its content
            function generateRowId(row) {
                return row.querySelector('td:nth-child(2)').textContent.trim();
            }

            // Check local storage and hide rows accordingly
            const bannedUsers = JSON.parse(localStorage.getItem('bannedUsers')) || [];
            bannedUsers.forEach(id => {
                document.querySelectorAll('#userTable4 tbody tr').forEach(row => {
                    if (generateRowId(row) === id) {
                        row.remove();
                    }
                });
            });

            // Attach click event listener to ban buttons
            document.querySelectorAll('.ban').forEach(button => {
                button.addEventListener('click', function () {
                    const row = this.closest('tr');
                    const userId = generateRowId(row);

                    // Remove the row from the table
                    row.remove();

                    // Update local storage
                    const bannedUsers = JSON.parse(localStorage.getItem('bannedUsers')) || [];
                    if (!bannedUsers.includes(userId)) {
                        bannedUsers.push(userId);
                        localStorage.setItem('bannedUsers', JSON.stringify(bannedUsers));
                    }
                });
            });
        });

    </script>

    <script>
        $(document).ready(function () {
            $('.enveloppe').on('click', function () {
                var slug = $(this).data('slug');  // Get the slug from the data attribute
                $('#emailModal').modal('show');

                // Use off to remove previous event handlers
                $('#sendMailBtn').off('click').on('click', function () {
                    window.location.href = '/students/email/' + slug;  // Use the slug in the URL
                });
            });

            $('.ban').on('click', function () {
                var slug = $(this).data('slug');  // Get the slug from the data attribute
                $(this).closest('tr').remove();  // Assuming you want to remove the row
                // Optional: Make an AJAX call here to handle the ban action on the server-side
            });
        });
    </script>

    <script>
    $(document).ready(function () {
        // Initialize DataTable with a page length of 5 rows
        $('#userTable').DataTable({
            "pageLength": 5
        });
        $('#userTable2').DataTable({
            "pageLength": 5
        });
        $('#userTable3').DataTable({
            "pageLength": 5
        });
        $('#userTable4').DataTable({
            "pageLength": 5
        });
    });
    </script>
    <script>
        // Your web app's Firebase configuration
        var firebaseConfig = {
          // Add your Firebase config here
        };
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
      </script>
    <!-- Progresive Web App  -->

    <script src="/static/js/service_worker.js">
                if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/static/js/service-worker.js')
                    .then((registration) => {
                        console.log('Service Worker registered successfully:', registration.scope);
                    })
                    .catch((error) => {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }
    </script>
  <script>
    function showNotification(title, body) {
      const options = {
        body: body,
        icon: '/static/img/librainian-logo-black-transparent.png',
        badge: '/static/img/notification-badge.png'
      };

      new Notification(title, options);
    }

    // Usage
    showNotification('Welcome!', 'Thank you for notifications for Librainian Admin Dashboard.');
  </script>
    <script>
        $(document).ready(function () {
            // Initialize Monthly Sales Chart
            var ctxMonthlySales = document.getElementById('monthlySalesChart').getContext('2d');
            var monthlySalesChart = new Chart(ctxMonthlySales, {
                type: 'line',
                data: {
                    labels: {{ sales_months|safe }},
                    datasets: [{
                        label: 'Sales Amount',
                        data: {{ sales_amounts|safe }},
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Initialize User Growth Chart
            var ctxUserGrowth = document.getElementById('userGrowthChart').getContext('2d');
            var userGrowthChart = new Chart(ctxUserGrowth, {
                type: 'bar',
                data: {
                    labels: {{ growth_months|safe }},
                    datasets: [{
                        label: 'User Count',
                        data: {{ user_counts|safe }},
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>



</body>

</html>