from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.models import User, Group
from django.contrib.auth import logout
from .models import LibraryCommander_param
from Library.user_auth import *
from django.contrib.auth.decorators import login_required
import json
from django.http import JsonResponse
from django.http import HttpResponse, Http404
import os
import subprocess

from django.http import FileResponse, HttpResponseNotFound, HttpResponseForbidden
from django.conf import settings


def librarycommander_signup(request, *args, **kwargs):
    if request.method == "POST":
        try:
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            librarycommander = create_user_and_login(request)

            if librarycommander:
                LibraryCommander_param.objects.create(
                    user=librarycommander,
                    librarycommander_phone_num=phone,
                    librarycommander_address=address,
                )
                group = Group.objects.get(name="librarycommander")
                print(group)
                librarycommander.groups.add(group)


                return redirect("/librarycommander/login/")
            else:
                messages.error(request, "Failed to create user. Please try again.")
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
    else:
        pass

    return render(request, "signup.html", {"role": "librarycommander"})


def librarycommander_login(request, *args, **kwargs):
    if request.method == "POST":
        try:
            librarycommander = authenticate_and_login(request, "librarycommander")
            if librarycommander:
                return redirect("/librarycommander/dashboard/")
            else:
                messages.error(
                    request, "Invalid credentials or you do not have admin access."
                )
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    return render(request, "login.html", {"role": "librarycommander"})


def librarycommander_logout(request, *args, **kwargs):
    try:
        logout(request)
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    return redirect("/")


@login_required(login_url="/librarian/login/")
def librarycommander_profile(request, *args, **kwargs):
    lib_com_profile = LibraryCommander_param.objects.get(user=request.user)

    return render(
        request,
        "user_profile.html",
        {"lib": lib_com_profile, "role": "librarycommander"},
    )


@login_required
def edit_library_commander_profile(request):
    try:
        # Fetch the logged-in user and their library commander profile
        commander = request.user
        commander_profile = LibraryCommander_param.objects.get(user=commander)

        if request.method == "POST":
            # Get data from the form for the User model
            first_name = request.POST.get("first_name")
            last_name = request.POST.get("last_name")
            email = request.POST.get("email")

            # Get data for the LibraryCommander_param model
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            # Update the User model fields
            commander.first_name = first_name
            commander.last_name = last_name
            commander.email = email
            commander.save()

            # Update the LibraryCommander_param model fields
            commander_profile.librarycommander_phone_num = phone
            commander_profile.librarycommander_address = address
            commander_profile.save()

            messages.success(request, "Profile updated successfully.")
            return redirect("/librarycommander/profile/")  # Redirect to profile page

        # Prepopulate the form with current data
        return render(
            request,
            "edit_profile.html",
            {
                "profile": commander_profile,
                "commander": commander,
                "role": "librarycommander",
            },
        )
    except LibraryCommander_param.DoesNotExist:
        messages.error(request, "Library Commander profile not found.")
        return redirect(
            "/librarycommander/login/"
        )  # Redirect to login if no profile is found
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/librarycommander/edit-profile/")


@login_required(login_url="/librarycommander/login/")
def library_and_manager_data(request, *args, **kwargs):
    user = request.user
    library_commander = LibraryCommander_param.objects.get(user=user)

    if library_commander.is_admin and user.is_authenticated:

        manager_data = Manager_param.objects.filter(library_commander=library_commander)

        library_data = Librarian_param.objects.filter(manager__in=manager_data)

        return render(
            request,
            "table.html",
            {
                "manager_data": manager_data,
                "library_data": library_data,
                "role": "librarycommander",
            },
        )
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "librarycommander"})


@login_required(login_url="/librarycommander/login/")
def library_commander_dashboard(request, *args, **kwargs):
    return render(request, "dashboard.html", {"role": "librarycommander"})


@login_required(login_url="/librarycommander/login/")
def complaint_page(request):
    tickets = ComplaintTicket.objects.all()
    return render(
        request,
        "complaint_ticket.html",
        {"role": "librarycommander", "tickets": tickets},
    )


def get_ticket_details(request, ticket_id):
    try:
        ticket = ComplaintTicket.objects.get(id=ticket_id)
        data = {
            "id": ticket.ticket_number,
            "description": ticket.description,
            "status": ticket.status,
            "created_at": ticket.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": ticket.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
        }
        return JsonResponse({"success": True, **data})
    except ComplaintTicket.DoesNotExist:
        return JsonResponse({"success": False, "error": "Ticket not found"})
    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)})


@csrf_exempt
def update_ticket_status(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            ticket_id = data.get("ticket_id")
            new_status = data.get("status")

            # Fetch the ticket and update the status
            ticket = ComplaintTicket.objects.get(id=ticket_id)
            ticket.status = new_status
            ticket.save()

            return JsonResponse({"success": True})
        except ComplaintTicket.DoesNotExist:
            return JsonResponse({"success": False, "error": "Ticket not found"})
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)})
    else:
        return JsonResponse({"success": False, "error": "Invalid request method"})


@login_required(login_url="/librarycommander/login/")
def download_log(request, log_type=None):
    # Define paths to the log files in the system_logs directory
    access_log_path = os.path.join(settings.BASE_DIR, "system_logs", "access_log.txt")
    error_log_path = os.path.join(settings.BASE_DIR, "system_logs", "error_log.txt")

    # Check if log files exist
    if not os.path.exists(access_log_path):
        return HttpResponse("Access log does not exist.")
    if not os.path.exists(error_log_path):
        return HttpResponse("Error log does not exist.")

    # Display the latest 15 lines from each log if no specific log is requested
    if log_type is None:

        def get_last_lines(file_path, num_lines=15):
            try:
                with open(file_path, "r") as file:
                    return file.readlines()[-num_lines:]
            except (FileNotFoundError, IOError):
                return ["Log file not found or could not be read."]

        # Get the latest lines from both logs
        access_log_tail = get_last_lines(access_log_path)
        error_log_tail = get_last_lines(error_log_path)

        # Render the page with log snippets
        return render(
            request,
            "download_logs.html",
            {
                "access_log_tail": access_log_tail,
                "error_log_tail": error_log_tail,
                "access_log_path": access_log_path,
                "error_log_path": error_log_path,
                "role": "librarycommander",
            },
        )

    # Download the full log file when a specific log_type is requested
    log_path = (
        access_log_path
        if log_type == "access"
        else error_log_path if log_type == "error" else None
    )

    if log_path and os.path.exists(log_path):
        with open(log_path, "r") as file:
            response = HttpResponse(file.read(), content_type="text/plain")
            response["Content-Disposition"] = (
                f'attachment; filename="{log_type}_log.txt"'
            )
            return response
    else:
        raise Http404("Log file does not exist.")


@login_required(login_url="/librarycommander/login/")
def list_and_download_backups(request, filename=None):
    user = request.user
    library_commander = LibraryCommander_param.objects.get(user=user)
    # Use the BACKUP_FOLDER defined in settings
    backup_folder = settings.BACKUP_FOLDER
    print(backup_folder)

    # Ensure the user is authenticated before listing/downloading backups
    if not library_commander.is_admin and user.is_authenticated:
        return HttpResponseForbidden("Unauthorized")

    # If a filename is provided, return that file for download
    if filename:
        file_path = os.path.join(backup_folder, filename)
        if os.path.exists(file_path):
            response = FileResponse(open(file_path, "rb"))
            response["Content-Disposition"] = f'attachment; filename="{filename}"'
            return response
        else:
            return HttpResponseNotFound("File not found.")

    # Otherwise, list all files in the backup directory
    try:
        files = [
            f
            for f in os.listdir(backup_folder)
            if os.path.isfile(os.path.join(backup_folder, f))
        ]
    except FileNotFoundError:
        files = []  # Handle the case where the directory doesn't exist
    print(files)
    # Render the template with the file list
    return render(
        request, "list_backups.html", {"files": files, "role": "librarycommander"}
    )


@login_required(login_url="/librarycommander/login/")
def restore_database(request):
    if request.method == "POST":
        # Get form data from POST
        db_name = request.POST.get("db_name")
        db_user = request.POST.get("db_user")
        db_password = request.POST.get("db_password")
        db_host = request.POST.get("db_host", "localhost")
        db_port = int(request.POST.get("db_port", 5432))
        backup_file = request.FILES.get("backup_file")

        # Ensure the required fields are present
        if not db_name or not db_user or not db_password or not backup_file:
            messages.error(request, "Please provide all required fields.")
            return render(request, "restore_database.html")

        # Save the uploaded backup file temporarily
        backup_path = f"/tmp/{backup_file.name}"
        with open(backup_path, "wb") as f:
            for chunk in backup_file.chunks():
                f.write(chunk)

        try:
            # Set the environment variable for the database password
            os.environ["PGPASSWORD"] = db_password

            # Build the restore command
            restore_command = [
                "pg_restore",
                "--clean",
                "--if-exists",
                "--no-owner",
                "--host",
                db_host,
                "--port",
                str(db_port),
                "--username",
                db_user,
                "--dbname",
                db_name,
                backup_path,
            ]

            # Run the restore command
            subprocess.run(restore_command, check=True)

            messages.success(request, "Database restored successfully!")
        except subprocess.CalledProcessError as e:
            messages.error(request, f"Error restoring database: {str(e)}")
        finally:
            # Clean up the uploaded file
            if os.path.exists(backup_path):
                os.remove(backup_path)
            os.environ.pop("PGPASSWORD", None)

    return render(request, "restore_database.html", {"role": "librarycommander"})


@login_required(login_url="/librarycommander/login/")
def feedback_page(request):
    return render(request, "feedback.html", {"role": "librarycommander"})


def page_not_found_view(request, exception=None):
    return render(request, "404.html", {"role": "librarycommander"})

from blogs.models import Blog
from django.shortcuts import render, get_object_or_404
def blog_details(request, slug):
    blog = get_object_or_404(Blog, slug=slug)
    return render(request, 'user_blog.html', {'blog': blog})
