<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page immediately
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    setTimeout(function() {
                        loader.style.display = 'none';
                    }, 100); // Small delay to ensure everything is rendered
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration Form</title>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">

             <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>
        /* General body styling */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 20px;
        }

        /* Container styling */
        .container-fluid {
            display: flex;
            flex-wrap: wrap;
            min-height: 100vh;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            background-color: #fff;
        }

        .image-part {
            background-image: url('https://img.freepik.com/free-vector/employees-cv-candidates-resume-corporate-workers-students-id-isolate-flat-design-element-job-applications-avatars-personal-information-concept-illustration_335657-1661.jpg');
            background-repeat: no-repeat;
            background-position: center;
            flex: 0 0 40%;
            border-top-left-radius: 15px;
            border-bottom-left-radius: 15px;
        }

        .form-part {
            flex: 0 0 60%;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        @media (max-width: 768px) {

            .image-part,
            .form-part {
                flex: 0 0 100%;
            }

            .image-part {
                display: none;
            }

            .form-part {
                border-radius: 15px;
            }
        }

        /* Form styling */
        .form-part h2 {
            margin-bottom: 10px;
            color: #007bff;
        }

        .form-part hr {
            border-top: 2px solid #007bff;
            margin-bottom: 30px;
        }

        .form-group label {
            font-weight: bold;
            color: #333;
            display: inline-block;
            margin-bottom: 5px;
        }

        .form-group label.required::after {
            content: " *";
            color: red;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group input[type="email"],
        .form-group input[type="tel"],
        .form-group input[type="date"],
        .form-group select,
        .form-group input[type="file"] {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
            color: #333;
            margin-bottom: 15px;
            padding-left: 45px;
            /* Increased padding for icons */
            height: 50px;
            /* Adjusted height for better alignment */
        }

        .form-group input[type="file"] {
            padding-left: 12px;
            /* Reset padding for file input */
            height: auto;
            /* Reset height for file input */
        }

        .btn-submit {
            display: block;
            width: 100%;
            padding: 12px 20px;
            background-color: #007bff;
            color: #fff;
            font-size: 1em;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            margin-top: 10px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        .btn-submit:hover {
            background-color: #66b2ff;
        }

        /* Footer styling */
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 10px;
            }

            .footer img {
                width: 60%;
            }

        }

        /* Icons for each field */
        .form-group i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #007bff;
            font-size: 1.2em;
        }

        .form-group {
            position: relative;
        }
    </style>
</head>

<body>

    <div class="container-fluid form-container">
        <div class="image-part" aria-label="Illustration showing employee resumes and ID cards"></div>
        <!-- Added aria-label for accessibility -->
        <section class="form-part"> <!-- Changed to <section> for better semantics -->
            <div class="w-100">
                <h2 class="text-center">Student Registration Form</h2>
                <hr>
                <form action="" method="POST" enctype="multipart/form-data">
                    {% csrf_token %}

                    <fieldset class="form-group"> <!-- Changed to <fieldset> for better semantics -->
                        <i class="fas fa-book"></i>
                        <label for="courseName" class="required">Course Name</label>
                        <select class="form-control" id="courseName" name="course" required>
                            <option value="">Select a course</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </fieldset>
                    <div class="form-row">

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-user"></i>
                            <label for="name" class="required">Name</label>
                            <input type="text" class="form-control" id="name" name="name" placeholder="Enter your name"
                                required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                        </fieldset>
                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-user-tie"></i>
                            <label for="fatherName">Father's Name</label>
                            <input type="text" class="form-control" id="fatherName" name="fathername"
                                placeholder="Enter your father's name" pattern="[A-Za-z ]+"
                                title="Please enter only alphabets">
                        </fieldset>

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-birthday-cake"></i>
                            <label for="age">Age</label>
                            <input type="number" class="form-control" id="age" name="age" placeholder="Enter your age"
                                min="1" max="99">
                        </fieldset>
                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-venus-mars"></i>
                            <label for="gender" class="required">Gender</label>
                            <select class="form-control" id="gender" name="gender" required>
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </fieldset>

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-envelope"></i>
                            <label for="email" class="required">Email address</label>
                            <input type="email" class="form-control" id="email" name="email"
                                placeholder="Enter your email" required>
                        </fieldset>

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-phone"></i>
                            <label for="phone" class="required">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                placeholder="Enter your phone number" required maxlength="10" pattern="\d{10}"
                                title="Please enter a 10-digit phone number">
                        </fieldset>

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-map-marker-alt"></i>
                            <label for="locality" class="required">Locality</label>
                            <input type="text" class="form-control" id="locality" name="locality"
                                placeholder="Enter your locality" required pattern="[A-Za-z ]+"
                                title="Please enter only alphabets">
                        </fieldset>

                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-city"></i>
                            <label for="city" class="required">City</label>
                            <input type="text" class="form-control" id="city" name="city" placeholder="Enter your city"
                                required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                        </fieldset>
                    </div>
                    <fieldset class="form-group">
                        <i class="fas fa-map"></i>
                        <label for="states" class="required">State</label>
                        <select class="form-control" id="states" name="state" required>
                            <option value="">Select a state</option>
                            {% for state in states %}
                            <option value="{{ state.id }}">{{ state.name }}</option>
                            {% endfor %}
                        </select>
                    </fieldset>
                    <div class="form-row">
                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-calendar"></i>
                            <label for="rdate" class="required">Registration Date</label>
                            <input type="date" class="form-control" id="rdate" name="rdate" required>
                        </fieldset>
                        <fieldset class="form-group col-md-6">
                            <i class="fas fa-money-bill"></i>
                            <label for="regfees" class="required">Registration Fee</label>
                            <input type="number" class="form-control" id="regfees" name="regfees"
                                placeholder="Enter registration fee" required>
                        </fieldset>
                    </div>
                    <fieldset class="form-group">

                        <label for="image">Student Image</label>
                        <input type="file" class="form-control-file" id="image" name="image">
                    </fieldset>
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-submit me-5">Register</button>
                        <button type="button" class="btn btn-submit" onclick="window.history.back();">Back</button>
                    </div>


                </form>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <div class="footer">
        <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">        <!-- <p>Developed with passion by Librainian</p> -->
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const maxSize = 20 * 1024; // 20 KB in bytes

            if (file.size > maxSize) {
            alert('Image size must not exceed 20 KB. Please choose a smaller file.');
            this.value = ''; // Clear the file input
            }
        });
    </script>
    <script>
        // Existing validations (phone, name, age) remain unchanged

        // Function to allow only alphabets and spaces
        function allowOnlyAlphabets(input) {
            input.value = input.value.replace(/[^A-Za-z ]/g, '');
        }

        // Father's Name validation
        document.getElementById('fatherName').addEventListener('input', function (e) {
            allowOnlyAlphabets(this);
        });

        // Locality validation
        document.getElementById('locality').addEventListener('input', function (e) {
            allowOnlyAlphabets(this);
        });

        // City validation
        document.getElementById('city').addEventListener('input', function (e) {
            allowOnlyAlphabets(this);
        });
    </script>
    <script>
        // Phone number validation (from previous response)
        document.getElementById('phone').addEventListener('input', function (e) {
            this.value = this.value.replace(/\D/g, '');
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });

        // Name validation
        document.getElementById('name').addEventListener('input', function (e) {
            this.value = this.value.replace(/[^A-Za-z ]/g, '');
        });

        // Age validation
        document.getElementById('age').addEventListener('input', function (e) {
            this.value = this.value.replace(/\D/g, '');
            if (this.value.length > 2) {
                this.value = this.value.slice(0, 2);
            }
            if (parseInt(this.value) > 99) {
                this.value = '99';
            }
        });
    </script>
    <script>
        document.getElementById('phone').addEventListener('input', function (e) {
            // Remove non-digit characters
            this.value = this.value.replace(/\D/g, '');

            // Limit to 10 digits
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
    </script>

    <!-- Global Loader Script -->
     
</body>

</html>