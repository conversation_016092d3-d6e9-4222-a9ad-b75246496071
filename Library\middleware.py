import datetime
from django.conf import settings
from django.contrib.auth import logout
from django.utils.deprecation import MiddlewareMixin

from django.shortcuts import render
from django.urls import resolve, Resolver404
from django.http import HttpResponseNotFound


class SessionTimeoutMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if not request.user.is_authenticated:
            return

        current_time = datetime.datetime.now()
        last_activity = request.session.get("last_activity")

        if last_activity:
            last_activity_time = datetime.datetime.strptime(
                last_activity, "%Y-%m-%d %H:%M:%S.%f"
            )
            session_age = (current_time - last_activity_time).total_seconds()

            if session_age > settings.SESSION_COOKIE_AGE:
                logout(request)
                return

        request.session["last_activity"] = str(current_time)


class URLMatchingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            # Attempt to resolve the URL
            resolve(request.path)
        except Resolver404:
            # If URL does not match, render the 'Page Not Found' view
            return HttpResponseNotFound(render(request, "404.html"))

        response = self.get_response(request)
        return response
