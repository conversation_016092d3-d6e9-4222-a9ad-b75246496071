<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     
    <meta name="google" content="notranslate">
    <title>Shift</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="/static/css/shifts_list.css">

    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
     

</head>

<body>

    <!-- Sidebar -->
{% include "sidebar.html" %}
<br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
           DASHBOARD / SHIFTS
        </div>
    </div>
        <!-- container -->
        <div class="seats_container">
            <div class="container-fluid mt-1">
                
                <div class="row">
                    <div class="col-md-4 card p-4">
                        <h3 class="mb-4">{% if shift %}Edit Shift{% else %}Create Shift{% endif %}</h3>
                        <form method="post" action="/{{role}}/shifts/">
                            {% csrf_token %}
                            <div class="form-group">
                                <label for="name">Name:</label>
                                <input type="text" class="form-control" id="name" name="name"
                                    value="{{ shift.name|default_if_none:'' }}" required>
                            </div>
                            <div class="form-group">
                                <label for="time">Time Range:</label>
                                <input type="text" class="form-control" id="time" name="time"
                                    value="{{ shift.time_range|default_if_none:'' }}" required>
                            </div>
                            <div class="form-group">
                                <label for="price">Price:</label>
                                <input type="number" step="0.01" class="form-control" id="price" name="price"
                                    value="{{ shift.price|default_if_none:'' }}" required>
                            </div>
                            <button type="submit" class="btn btn-info">{% if shift %}Update{% else %}Create{% endif %}</button>
                            <a href="/librarian/shifts" class="btn btn-secondary">Cancel</a>
                        </form>
                    </div>

                    <div class="col-md-8">
                        <h3 class="shift-detail">Shift Details</h3>
                        {% if messages %}
                        <div id="messageContainer">
                            {% for message in messages %}
                            {% if message.tags == 'success' %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {% elif message.tags == 'error' %}
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {% elif message.tags == 'warning' %}
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        {% elif message.tags == 'info' %}
                                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                                            {% else %}
                                            <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                                {% endif %}
                                                {{ message }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Time Range</th>
                                        <th>Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for shift in shifts %}
                                    <tr>
                                        <td><a href="/librarian/shifts/update/{{ shift.id }}">{{ shift.name }}</a></td>
                                        <td>{{ shift.time_range }}</td>
                                        <td>{{ shift.price }}</td>
                                        <td>
                                            <a href="/librarian/shifts/update/{{ shift.id }}"
                                                class="btn btn-warning btn-sm">Edit</a>
                                            <button class="btn btn-danger btn-sm"
                                                onclick="confirmDelete('{{ shift.id }}')">Delete</button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
                
            </div>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        Are you sure you want to delete this shift?
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                    </div>
                </div>
            </div>
        </div>

        <form id="deleteForm" method="post" style="display:none;">
            {% csrf_token %}
        </form>


    </div>

    <div class="footer ">
        <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">                    <!-- <p>Developed with passion by Librainian</p> -->
    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
            // Show success message and hide after 10 seconds
            document.addEventListener('DOMContentLoaded', function () {
                var alerts = document.querySelectorAll('.alert');
                alerts.forEach(function (alert) {
                    setTimeout(function () {
                        alert.style.transition = 'opacity 1s';
                        alert.style.opacity = '0';
                        setTimeout(function () {
                            alert.style.display = 'none';
                        }, 1000);
                    }, 10000);
                });
            });
        </script>
    <script>
        function confirmDelete(shiftId) {
            $('#deleteModal').modal('show');
            document.getElementById('confirmDeleteBtn').setAttribute('data-shift-id', shiftId);
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', function () {
            var shiftId = this.getAttribute('data-shift-id');
            var deleteForm = document.getElementById('deleteForm');
            deleteForm.action = '/librarian/shifts/delete/' + shiftId + '/';
            deleteForm.submit();
        });
    </script>

    <script>
         
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
         <script>
            function updateDateTime() {
                const now = new Date();
                const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
                const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    
                document.getElementById('date').textContent = 'Date: ' + date;
                document.getElementById('time').textContent = 'Time: ' + time;
            }
    
            // Update the date and time on page load
            updateDateTime();
    
            // Update the date and time every minute
            setInterval(updateDateTime, 60000);
        </script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">
</body>

</html>