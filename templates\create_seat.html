
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seat allotment</title>
     
    <meta name="google" content="notranslate">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Admin Dashboard">
    <link rel="apple-touch-icon" href="/static/img/librainian-logo-black-transparent.png">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="/static/css/create_seat.css">
    <link rel="manifest" href="/static/js/manifest.json">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
   


    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

    


</head>



    {% include "sidebar.html" %}

    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 25rem;">
           DASHBOARD / SEAT MANAGEMENT
        </div>
    </div>


    <div class="main-container">
        <div class="header-section text-center">
            <h1><i class="fas fa-chair me-2"></i>Seat Management System</h1>
            <p>Create and manage seats for all shifts</p>
        </div>

        <!-- Seat Creation Form -->
            <div class="form-card">
            <form method="POST" action="{% url 'seats-list' %}">
                {% csrf_token %}
                <div class="mb-4">
                    <label for="seat_number" class="form-label">Seat Prefix:</label>
                    <input type="text" id="seat_number" name="prefix" class="form-control" placeholder="Enter seat prefix" >
                </div>
                <div class="mb-4">
                    <label for="seat_number" class="form-label">Total Seat Numbers:</label>
                    <input type="text" id="seat_number" name="total_seats" class="form-control" placeholder="Enter total seats number" required>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary"><i class="fas fa-plus-circle me-2"></i>Create Seat</button>
                    <a href="/{{role}}/dashboard/" class="btn btn-primary"><i class="fas fa-times-circle me-2"></i>Cancel</a>
                </div>
            </form>
        </div>

<!-- Seat Layout -->
<div class="seat-container">
    <h3 class="text-center pt-3">Shifts Present</h3>
    <div class="shift-btn-cont">
        <button class="shift-btn active" onclick="filterSeats('all')" style="border-color: #28a745;">All</button>
        {% for i in shifts %}
            <button class="shift-btn" onclick="filterSeats('{{ i }}')">{{ i }}</button>
        {% endfor %}
    </div>

    <h3 class="text-center mt-4 mb-4"><i class="fas fa-th me-2"></i>Seat Layout</h3>

    <!-- Legend for Seat Availability -->
    <div class="legend" id="seatFilter">
        <div id="all" class="curr legend-item active" data-filter="all">
            <div class="legend-box" style="background-color: #6c757d"></div>
            <span>All Seats</span>
        </div>
        <div id="available" class="legend-item" data-filter="available">
            <div class="legend-box available"></div>
            <span>Available</span>
        </div>
        <div id="occupied" class="legend-item" data-filter="occupied">
            <div class="legend-box occupied"></div>
            <span>Occupied</span>
        </div>
    </div>

    <!-- Display Seats -->
    <div class="seats_flex">
        {% for seat in seat_list %}
        <div class="seat-wrapper">
            <div class="seat-controls">
                <button class="info-btn" title="Info" data-bs-toggle="modal" data-bs-target="#seatInfoModal{{ seat.id }}">
                    <i class="fas fa-info-circle"></i>
                </button>
                
                <form method="GET" action="{% url 'cancel_seat' seat.id %}" style="display: inline;">
                    <button type="submit" class="cancel-btn" title="Cancel">
                        <i class="fas fa-times-circle"></i>
                    </button>
                </form>
                <form method="GET" action="{% url 'delete_seat' seat.id %}" style="display: inline;">
                    <button type="submit" class="delete-btn" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </form>
            </div>

            <div class="seat-box {% if seat.is_available %}available{% else %}occupied{% endif %}" 
                 data-status="{% if seat.is_available %}available{% else %}occupied{% endif %}"
                 data-seat-id="{{ seat.id }}"
                 data-seat-number="{{ seat.seat_number }}"
                 data-shift="{{ seat.shift }}">
                <i class="fas fa-chair d-block" style="font-size: 2rem;"></i>
                {{ seat.seat_number }}
            </div>
        </div>

        <!-- Info Modal for each seat -->
        <div class="modal fade" id="seatInfoModal{{ seat.id }}" tabindex="-1" aria-hidden="true" >
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Seat Information</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>Seat Number:</strong> {{ seat.seat_number }}</p>
                        <p><strong>Shift:</strong> {{ seat.shift }}</p>
                        <p><strong>Status:</strong> {% if seat.is_available %}Available{% else %}Occupied{% endif %}</p>
                        {% if not seat.is_available %}
                            {% for booking in seat.booking_set.all %}
                                <p><strong>Assigned To:</strong> {{ booking.student.name }}</p>
                            {% endfor %}
                        {% endif %}
                        {% if seat.last_modified %}
                        <p><strong>Last Modified:</strong> {{ seat.last_modified }}</p>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="text-center text-muted">
            <i class="fas fa-info-circle me-2"></i>No seats available
        </div>
        {% endfor %}
    </div>
</div>

<!-- Footer -->
<div class="footer">
    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
</div>

<!-- Scripts -->

<!-- very imporant bootstrap script for seat info, logout and profile model  -->

<!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Popper.js (necessary for Bootstrap's dropdowns and tooltips) -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>

<!-- Bootstrap 4 JavaScript -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


<script>
    let selectedFilter = 'all'; // Default availability filter
    let selectedShift = 'all';  // Default shift filter

    // Function to filter seats based on selected shift and availability status
    function filterSeats(shift) {
        // Update the active shift button
        document.querySelectorAll('.shift-btn').forEach(btn => {
            btn.classList.remove('active');
            btn.style.borderColor = ''; // Reset border color
        });
        const activeBtn = document.querySelector(`[onclick="filterSeats('${shift}')"]`);
        activeBtn.classList.add('active');
        activeBtn.style.borderColor = '#28a745'; // Add border color for active shift

        // Update the selected shift
        selectedShift = shift;

        // Apply the combined filters
        applyFilters();
    }

    // Function to apply both shift and availability filters
    function applyFilters() {
        document.querySelectorAll('.seat-box').forEach(seat => {
            const seatStatus = seat.dataset.status;
            const seatShift = seat.dataset.shift;

            // Show seats that match both filters
            if ((selectedShift === 'all' || seatShift === selectedShift) &&
                (selectedFilter === 'all' || seatStatus === selectedFilter)) {
                seat.style.display = 'inline-block';
            } else {
                seat.style.display = 'none';
            }
        });
    }

    // Availability filter event listeners
    document.querySelectorAll('.legend-item').forEach(item => {
        item.addEventListener('click', function() {
            // Update active class
            document.querySelectorAll('.legend-item').forEach(btn => btn.classList.remove('curr'));
            this.classList.add('curr');

            // Update the selected filter and apply filters
            selectedFilter = this.dataset.filter;
            applyFilters();
        });
    });

    // Initialize filters on page load
    document.addEventListener('DOMContentLoaded', applyFilters);

    // Seat controls functionality
    let activeControls = null;
    document.querySelectorAll('.seat-box').forEach(seat => {
        seat.addEventListener('click', function(e) {
            const controls = this.parentElement.querySelector('.seat-controls');

            // Hide previously active controls
            if (activeControls && activeControls !== controls) {
                activeControls.classList.remove('active');
            }

            // Toggle controls for clicked seat
            controls.classList.toggle('active');
            activeControls = controls.classList.contains('active') ? controls : null;

            e.stopPropagation();
        });
    });

    // Close controls when clicking outside
    document.addEventListener('click', function(e) {
    if (!e.target.closest('.seat-wrapper')) {
        // Close active seat controls
        if (activeControls) {
            activeControls.classList.remove('active');
            activeControls = null;
        }
    }
});


    // Prevent controls from closing when clicking buttons
    document.querySelectorAll('.seat-controls button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Delete confirmation
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to delete this seat?')) {
                this.submit();
            }
        });
    });
</script>


</body>
</html>

