body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Arial', 'Helvetica', sans-serif;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #cee3e0 !important;
    color: #333;
}
.container {
    margin-left: 1rem;
    margin-right: 1rem;
    margin-top: 50px;
    margin-bottom: 200px;
    max-width: 800px;
    background-color: #ffffff;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}
@media (min-width: 768px) {
    .container {
        padding: 40px;
    }
}
.header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}
.header h2 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 24px;
    margin-bottom: 10px;
}
.header p {
    color: #7f8c8d;
    font-size: 14px;
}
.plan-details {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 30px;
}
@media (min-width: 768px) {
    .plan-details {
        padding: 25px;
    }

   
}

@media only screen and (max-width: 500px) {
    .container {
        width: 95% !important;
    }
}
.plan-details h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 18px;
}
.list-group-item {
    background-color: transparent;
    border: none;
    padding: 8px 0;
    color: #495057;
    font-size: 14px;
    display: flex;
    align-items: center;
    word-break: break-word;
}
.list-group-item i {
    margin-right: 10px;
    color: #3498db;
    width: 20px;
}
.btn-custom {
    padding: 10px 20px;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-radius: 4px;
}
.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
}
.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}
.btn-outline-secondary {
    color: #7f8c8d;
    border-color: #7f8c8d;
}
.btn-outline-secondary:hover {
    background-color: #7f8c8d;
    color: #ffffff;
}
.footer {
    padding: 20px;
    background-color: #2c3e50;
    color: #ffffff;
    text-align: center;
}
.footer p {
    margin: 0;
    font-size: 14px;
}
.footer a {
    color: #3498db;
    text-decoration: none;
}
.footer a:hover {
    text-decoration: underline;
}