  <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="google" content="notranslate" />
    <meta
      content="{{blog.description}}"
      name="description"
      id="metaDescription"
    />
    <meta content="{{ blog.keyword }}" name="keywords" />
    <meta name="robots" content="index,follow" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta http-equiv="content-language" content="en" />
    <meta name="geo.region" content="IN" />
    <meta property="og:title" content="{{blog.title}}" />
    <meta
      property="og:description"
      content="{{blog.description}}"
      id="ogDescription"
    />
    <link rel="canonical" href="https://librainian.com/blogs/p/{{blog.slug}}/" />
    <meta
      property="og:url"
      content="https://librainian.com/blogs/p/{{blog.slug}}/"
    />
    <meta property="og:image" content="https://librainian.com{{ blog.image.url }}" />
    <meta
      itemprop="author"
      content="{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}"
    />
    <meta itemprop="datePublished" content="{{ blog.published_date }}" />
    <meta itemprop="dateModified" content="{{ blog.updated_at }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{blog.title}}" />
    <meta
      name="twitter:description"
      content="{{blog.description}}"
      id="twitterDescription"
    />
    <meta name="twitter:image" content="https://librainian.com{{ blog.image.url }}" />
    <title>{{ blog.title }} | Librainian</title>
    <!-- favicon and other links -->
    <link
      href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png"
      rel="icon"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&family=Playfair+Display:wght@700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
      rel="stylesheet"
    />



    <script>
      function trimDescription(description, maxLength) {
        if (description.length > maxLength) {
          return description.substring(0, maxLength - 3) + "..."; // 3 dots ke liye space chhodna
        }
        return description;
      }

      document.addEventListener("DOMContentLoaded", function () {
        var metaDescription = "{{ blog.description|escapejs }}"; // Escape JS to avoid issues
        var trimmedDescription = trimDescription(metaDescription, 160);

        // Set the content for meta tags
        document
          .getElementById("metaDescription")
          .setAttribute("content", trimmedDescription);
        document
          .getElementById("ogDescription")
          .setAttribute("content", trimmedDescription);
        document
          .getElementById("twitterDescription")
          .setAttribute("content", trimmedDescription);
      });
    </script>

    <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

    

    <style>
      body {
        -webkit-user-select: none; /* Disable text selection */
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        margin: 0;
      }

      :root {
        --primary-color: #1a1a1a;
        --secondary-color: #4a4a4a;
        --accent-color: #3498db;
        --background-color: #f7f7f7;
        --text-color: #333;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: var(--text-color);
        background-color: var(--background-color);
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      header {
        background-color: var(--primary-color);
        color: #fff;
        padding: 10px 0;
      }

      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .navbar a {
        color: #fff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 600;
      }

      .navbar a:hover {
        color: var(--accent-color);
      }

      .blog-header {
        padding: 20px 0;
        text-align: center;
      }

      .blog-title {
        font-family: "Playfair Display", serif;
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
      }

      .blog-subtitle {
        font-size: 1.2rem;
        color: #bbb;
      }

      .blog_description {
        font-size: 1.2rem;
      }

      .content-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }

      .blog-content {
        flex: 0 0 70%;
        max-width: 70%;
        background-color: #fff;
        padding: 2rem;
        margin-top: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }

      .blog-image {
        width: 100%;
        height: 100vh;
        display: block;
        margin: 0 auto;
        /* Center the image */
      }

      .author-info {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .info_box {
    font-size: 1.2rem;
    line-height: 1.5;
    font-weight: bold;
    background-color: #efefef;
    color: #3a3a3a;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 4.5rem;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
  }

      .author-name {
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--secondary-color);
      }

      .date-info {
        font-size: 0.8rem;
        color: #777;
      }

      .blog-description {
        font-style: italic;
        font-size: 0.95rem;
        color: var(--secondary-color);
        border-left: 3px solid var(--accent-color);
        padding-left: 1rem;
        margin-bottom: 1.5rem;
      }
      .blog-description2 {
        font-style: italic;
        font-size: 0.95rem;
        color: var(--secondary-color);
        /* border-left: 3px solid var(--accent-color); */
        /* padding-left: 1rem; */
        margin-bottom: 1.5rem;
      }

      .blog-body {
        font-size: 0.95rem;
      }

      .blog-body h2 {
        font-size: 1.4rem;
        color: var(--primary-color);
        margin-top: 2rem;
        margin-bottom: 1rem;
      }

      .blog-body p {
        margin-bottom: 1rem;
      }

      .related-articles {
        flex: 0 0 calc(30% - 20px);
        max-width: calc(30% - 20px);
      }

      .related-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: transform 0.3s ease;
        text-decoration: none;
      }

      .related-card:hover {
        transform: scale(1.05);
      }

      .related-card-content {
        padding: 15px;
      }

      .related-card-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
        color: black;
      }

      .related-card-author {
        font-size: 0.9rem;
        color: #666;
      }

      .related-card-date {
        font-size: 0.8rem;
        color: #999;
      }

      .social-media {
        display: flex;
        justify-content: center;
        margin: 20px 0;
      }

      .social-media a {
        color: var(--primary-color);
        font-size: 1.5rem;
        margin: 0 10px;
      }

      .social-media a:hover {
        color: var(--accent-color);
      }

      footer {
        background-color: var(--primary-color);
        color: #fff;
        text-align: center;
        padding: 20px 0;
      }

      footer .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }

      footer a {
        color: #fff;
        text-decoration: none;
        margin: 0 10px;
        font-size: 0.85rem;
      }

      footer a:hover {
        color: var(--accent-color);
      }

      .breadcrumb-item {
        transition: all 0.3s ease;
      }

      .breadcrumb-item:hover {
        transform: translateY(-2px);
      }

      .breadcrumb-item + .breadcrumb-item::before {
        content: "/";
        color: #6c757d;
      }

      .breadcrumb-item a {
        color: #0a6a6d;
        text-decoration: none;
        position: relative;
      }

      .breadcrumb-item a::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 2px;
        bottom: -2px;
        left: 0;
        background-color: #0a6a6d;
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .breadcrumb-item a:hover::after {
        transform: scaleX(1);
      }

      .breadcrumb-item.active {
        font-weight: bold;
      }

      .breadcrumb {
        margin-top: 10px;
        margin-bottom: -10px;
      }

      @media (max-width: 992px) {
        .blog-content,
        .related-articles {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }

      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.75rem;
        }

        .blog-content {
          padding: 1.5rem;
        }

        .blog-image {
          height: 250px;
        }

        .navbar a {
          margin: 0 10px;
        }

        .social-media a {
          font-size: 1.5rem;
          margin: 0 5px;
        }
      }

      @media (max-width: 480px) {
        body {
          font-size: 13px;
        }

        .blog-title {
          font-size: 1.5rem;
        }

        .blog-content {
          padding: 1rem;
        }

        .blog-image {
            width: 400px; /* Fixed width for the image */
    height: 300px;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .navbar a {
          margin: 0 5px;
        }

        .social-media a {
          font-size: 1.25rem;
          /* margin: 0 3px; */
          margin-left: 20px;
        }

        .breadcrumb-item {
          font-size: 0.9rem;
        }
      }
    </style>
  </head>

  <body>

    <header>
      <nav class="navbar">
        <div class="navbar-brand text-white">
          <a href="/blogs/p">Blog</a>
        </div>
        <div class="d-flex justify-content-end align-items-center">
          <div class="navbar-menu">
            <a href="/">Home</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/library-list/">Library List</a>
          </div>
          <div class="navbar-menu">
            <a href="/membership/plans/">Pricing</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/login/" class="btn btn-outline-info">Login</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/signup/" class="btn btn-info">Signup</a>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-fluid">
      <div class="content-wrapper">
        <article class="blog-content">
          <div aria-label="breadcrumb mt-5">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="/">Home</a></li>
              <li class="breadcrumb-item"><a href="/blogs/p">Blogs</a></li>
              <li class="breadcrumb-item">
                <a href="/blogs/p/cat/{{ blog.category }}/"
                  >{{ blog.category }}</a
                >
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                {{ blog.title }}
              </li>
            </ol>
          </div>
          <div class="d-flex justify-content-between border-bottom pt-4">
            <a
              href="https://librainian.com/"
              class="text-decoration-none text-dark"
              >Librainian.com</a
            >
            <span>Published on: {{ blog.published_date|date }}</span>
          </div>
          <div class="blog-header">
            <h1 class="blog-title">{{ blog.title }}</h1>
          </div>
          <p class="mt-2 mb-2 text-muted blog_description">
            <em>{{ blog.description }}</em>
          </p>
          <div class="d-flex justify-content-between align-items-center gap-3 mb-2">
            <div class="col-12 col-md-6 info_box">Updated On: {{ blog.updated_at|date }}</div>
            <div class="col-12 col-md-6 info_box">Author: {{ blog.author.user.first_name }} {{ blog.author.user.last_name }}</div>
          </div>
          <!-- <div class="author-info">
            <div>
              <p class="author-name">
                Author - {{ blog.author.user.first_name }} {{
                blog.author.user.last_name }}
              </p>
              <div class="date-info">
                <span>Published: {{ blog.date_created }}</span>
                <span class="ml-2"> | Updated: {{ blog.date_updated }}</span>
              </div>
            </div>
          </div> -->
          <blockquote class="blog-description">
            {{ blog.introduction }}dsfsfsf
          </blockquote>
          <div style="text-align: center">
            <!-- Centering div -->
            <img
              src="{{ blog.image.url }}"
              class="blog-image"
              alt="{{ blog.title }}"
              loading="lazy"
            />
          </div>
          <div class="blog-body">{{ blog.content|safe }}</div>
        </article>

        <aside class="related-articles mt-3">
          <h3>Related Articles</h3>
          <div id="relatedArticlesContainer">
            {% for blog in blogs %}
            <div class="related-card" data-entry="{{ forloop.counter }}">
              <a href="/blogs/p/{{blog.slug}}/" class="related-card">
                <div class="related-card-content">
                  <h4 class="related-card-title">{{ blog.title }}</h4>
                  <p class="related-card-date">
                    Published: {{ blog.date_created }}
                  </p>
                  <p class="blog-description2">
                    {{ blog.description|truncatewords:20|safe }}
                  </p>
                </div>
              </a>
            </div>
            {% if forloop.counter|divisibleby:10 and not forloop.last %}
          </div>
          <div id="relatedArticlesContainer" style="display: none">
            {% endif %} {% endfor %}
          </div>
          <nav aria-label="Related articles pagination">
            <ul class="pagination" id="relatedArticlesPagination"></ul>
          </nav>
        </aside>
      </div>
    </main>

    <div class="social-media">
      <a href="https://www.facebook.com/profile.php?id=61562707884730"
        ><i class="fab fa-facebook-f"></i
      ></a>
      <a href="https://x.com/Librainian_app?t=ge9yi-sL_8SFXeWGZS9apQ&s=09"
        ><i class="fab fa-twitter"></i
      ></a>
      <a href="https://www.instagram.com/librainian.app/"
        ><i class="fab fa-instagram"></i
      ></a>
      <a href="https://www.linkedin.com/company/pinak-venture/"
        ><i class="fab fa-linkedin-in"></i
      ></a>
    </div>

    <footer>
      <div class="container">
        <div>&copy; 2024 Librainian. All rights reserved.</div>
        <div>
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
          <a href="#">Contact Us</a>
        </div>
      </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
      let currentPage = 1;
      const entriesPerPage = 10;

      function showPage(page) {
        const containers = document.querySelectorAll(
          "#relatedArticlesContainer"
        );
        containers.forEach((container, index) => {
          if (index === page - 1) {
            container.style.display = "block";
          } else {
            container.style.display = "none";
          }
        });
      }

      function updatePagination() {
        const containers = document.querySelectorAll(
          "#relatedArticlesContainer"
        );
        const totalPages = containers.length;
        const paginationElement = document.getElementById(
          "relatedArticlesPagination"
        );
        paginationElement.innerHTML = "";

        for (let i = 1; i <= totalPages; i++) {
          const li = document.createElement("li");
          li.className = `page-item ${i === currentPage ? "active" : ""}`;
          li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
          paginationElement.appendChild(li);
        }
      }

      document
        .getElementById("relatedArticlesPagination")
        .addEventListener("click", (e) => {
          e.preventDefault();
          if (e.target.tagName === "A") {
            currentPage = parseInt(e.target.dataset.page);
            showPage(currentPage);
            updatePagination();
          }
        });

      // Initial setup
      showPage(currentPage);
      updatePagination();
    </script>
    <script>
      window.addEventListener("load", function () {
          const path = window.location.pathname; // Get current page path
          let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

          // Increment count for the current path
          pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

          // Store updated data back to localStorage
          localStorage.setItem("page_data", JSON.stringify(pageData));
        });

        // Function to send page view data
        function sendPageData() {
          const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

          if (Object.keys(pageData).length > 0) {
            fetch(location.origin + "/librarian/track-page-view/", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": "{{ csrf_token }}",
              },
              body: JSON.stringify(pageData),
            })

              .then(() => {
                localStorage.removeItem("page_data");
              })
              .catch((error) => console.error("Error sending page data:", error));
              localStorage.removeItem("page_data");
          } else {

            console.log("No page data to send");
          }
        }

        // Send data every 10 seconds
        setInterval(sendPageData, 10000);
  </script>
  </body>
</html>
