  <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="google" content="notranslate" />
    <meta
      content="{{blog.description}}"
      name="description"
      id="metaDescription"
    />
    <meta content="{{ blog.keyword }}" name="keywords" />
    <meta name="robots" content="index,follow" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta http-equiv="content-language" content="en" />
    <meta name="geo.region" content="IN" />
    <meta property="og:title" content="{{blog.title}}" />
    <meta
      property="og:description"
      content="{{blog.description}}"
      id="ogDescription"
    />
    <link rel="canonical" href="https://librainian.com/blogs/p/{{blog.slug}}/" />
    <meta
      property="og:url"
      content="https://librainian.com/blogs/p/{{blog.slug}}/"
    />
    <meta property="og:image" content="https://librainian.com{{ blog.image.url }}" />
    <meta
      itemprop="author"
      content="{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}"
    />
    <meta itemprop="datePublished" content="{{ blog.published_date }}" />
    <meta itemprop="dateModified" content="{{ blog.updated_at }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{blog.title}}" />
    <meta
      name="twitter:description"
      content="{{blog.description}}"
      id="twitterDescription"
    />
    <meta name="twitter:image" content="https://librainian.com{{ blog.image.url }}" />

    <!-- Additional SEO Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">

    <title>{{ blog.title }} | Librainian</title>

    <!-- JSON-LD Structured Data for SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "{{ blog.title|escapejs }}",
      "description": "{{ blog.description|escapejs }}",
      "image": {
        "@type": "ImageObject",
        "url": "https://librainian.com{{ blog.image.url }}",
        "width": 1200,
        "height": 630
      },
      "author": {
        "@type": "Person",
        "name": "{{ blog.author.user.first_name }} {{ blog.author.user.last_name }}",
        "url": "https://librainian.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Librainian",
        "logo": {
          "@type": "ImageObject",
          "url": "https://librainian.com/static/img/librainian-logo-black-transparent.png",
          "width": 200,
          "height": 200
        }
      },
      "datePublished": "{{ blog.published_date|date:'c' }}",
      "dateModified": "{{ blog.updated_at|date:'c' }}",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://librainian.com/blogs/p/{{ blog.slug }}/"
      },
      "articleSection": "{{ blog.category }}",
      "keywords": "{{ blog.keyword }}",
      "wordCount": "{{ blog.content|striptags|wordcount }}",
      "inLanguage": "en-IN",
      "isAccessibleForFree": true
    }
    </script>

    <!-- favicon and other links -->
    <link
      href="/static/img/librainian-logo-black-transparent.png"
      rel="icon"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&family=Playfair+Display:wght@700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0"
      crossorigin="anonymous"
    />



    <script>
      function trimDescription(description, maxLength) {
        if (description.length > maxLength) {
          return description.substring(0, maxLength - 3) + "..."; // 3 dots ke liye space chhodna
        }
        return description;
      }

      document.addEventListener("DOMContentLoaded", function () {
        var metaDescription = "{{ blog.description|escapejs }}"; // Escape JS to avoid issues
        var trimmedDescription = trimDescription(metaDescription, 160);

        // Set the content for meta tags
        document
          .getElementById("metaDescription")
          .setAttribute("content", trimmedDescription);
        document
          .getElementById("ogDescription")
          .setAttribute("content", trimmedDescription);
        document
          .getElementById("twitterDescription")
          .setAttribute("content", trimmedDescription);
      });
    </script>

    <style>
      body {
        -webkit-user-select: none; /* Disable text selection */
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        margin: 0;
      }

      :root {
        --primary-color: #1a1a1a;
        --secondary-color: #4a4a4a;
        --accent-color: #3498db;
        --background-color: #f7f7f7;
        --text-color: #333;
        --border-color: #e0e0e0;
        --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.05);
        --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.1);
        --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Inter", sans-serif;
        font-size: 16px;
        line-height: 1.7;
        color: var(--text-color);
        background-color: var(--background-color);
        font-weight: 400;
        letter-spacing: 0.01em;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      header {
        background-color: var(--primary-color);
        color: #fff;
        padding: 10px 0;
      }

      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .navbar a {
        color: #fff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 600;
      }

      .navbar a:hover {
        color: var(--accent-color);
      }

      .blog-header {
        padding: 20px 0;
        text-align: center;
      }

      .blog-title {
        font-family: "Playfair Display", serif;
        font-size: 3rem;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 1rem;
        color: var(--primary-color);
        letter-spacing: -0.02em;
        text-align: center;
      }

      .blog-subtitle {
        font-size: 1.3rem;
        color: var(--secondary-color);
        font-weight: 500;
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .blog_description {
        font-size: 1.2rem;
      }

      .content-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }

      .blog-content {
        flex: 0 0 70%;
        max-width: 70%;
        background-color: #fff;
        padding: 3rem;
        margin-top: 20px;
        box-shadow: var(--shadow-light);
        border-radius: 8px;
        border: 1px solid var(--border-color);
        transition: var(--transition-smooth);
      }

      .blog-content:hover {
        box-shadow: var(--shadow-medium);
      }

      .blog-image {
        width: 100%;
        height: 100vh;
        display: block;
        margin: 0 auto;
        /* Center the image */
      }

      .author-info {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .info_box {
        font-size: 1rem;
        line-height: 1.5;
        font-weight: 600;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 4rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-light);
        transition: var(--transition-smooth);
      }

      .info_box:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
      }

      .author-name {
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--secondary-color);
      }

      .date-info {
        font-size: 0.8rem;
        color: #777;
      }

      .blog-description {
        font-style: italic;
        font-size: 1.1rem;
        color: var(--secondary-color);
        border-left: 4px solid var(--accent-color);
        padding: 1.5rem;
        margin: 2rem 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 8px;
        box-shadow: var(--shadow-light);
        position: relative;
      }

      .blog-description::before {
        content: '"';
        font-size: 3rem;
        color: var(--accent-color);
        position: absolute;
        top: -10px;
        left: 10px;
        font-family: "Playfair Display", serif;
      }

      .blog-description2 {
        font-style: italic;
        font-size: 1rem;
        color: var(--secondary-color);
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .blog-body {
        font-size: 1.1rem;
        line-height: 1.8;
        color: var(--text-color);
      }

      .blog-body h1,
      .blog-body h2,
      .blog-body h3,
      .blog-body h4,
      .blog-body h5,
      .blog-body h6 {
        font-family: "Playfair Display", serif;
        color: var(--primary-color);
        margin-top: 2.5rem;
        margin-bottom: 1.5rem;
        font-weight: 700;
        line-height: 1.3;
      }

      .blog-body h2 {
        font-size: 2rem;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 0.5rem;
      }

      .blog-body h3 {
        font-size: 1.6rem;
      }

      .blog-body h4 {
        font-size: 1.4rem;
      }

      .blog-body p {
        margin-bottom: 1.5rem;
        text-align: justify;
      }

      .blog-body ul,
      .blog-body ol {
        margin-bottom: 1.5rem;
        padding-left: 2rem;
      }

      .blog-body li {
        margin-bottom: 0.5rem;
      }

      .blog-body blockquote {
        border-left: 4px solid var(--accent-color);
        padding: 1rem 1.5rem;
        margin: 2rem 0;
        background-color: #f8f9fa;
        font-style: italic;
        border-radius: 4px;
      }

      .related-articles {
        flex: 0 0 calc(30% - 20px);
        max-width: calc(30% - 20px);
      }

      .related-articles h3 {
        font-family: "Playfair Display", serif;
        font-size: 1.8rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--accent-color);
      }

      .related-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: var(--shadow-light);
        margin-bottom: 20px;
        overflow: hidden;
        transition: var(--transition-smooth);
        text-decoration: none;
        border: 1px solid var(--border-color);
      }

      .related-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
        text-decoration: none;
      }

      .related-card-content {
        padding: 1.5rem;
      }

      .related-card-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.8rem;
        color: var(--primary-color);
        line-height: 1.4;
        font-family: "Playfair Display", serif;
      }

      .related-card-author {
        font-size: 0.9rem;
        color: var(--secondary-color);
        font-weight: 500;
      }

      .related-card-date {
        font-size: 0.85rem;
        color: #999;
        margin-bottom: 0.8rem;
      }

      .social-media {
        display: flex;
        justify-content: center;
        margin: 3rem 0;
        padding: 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        box-shadow: var(--shadow-light);
      }

      .social-media a {
        color: var(--primary-color);
        font-size: 1.8rem;
        margin: 0 15px;
        padding: 12px;
        border-radius: 50%;
        background-color: #fff;
        box-shadow: var(--shadow-light);
        transition: var(--transition-smooth);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
      }

      .social-media a:hover {
        color: #fff;
        background-color: var(--accent-color);
        transform: translateY(-3px);
        box-shadow: var(--shadow-medium);
      }

      footer {
        background-color: var(--primary-color);
        color: #fff;
        text-align: center;
        padding: 20px 0;
      }

      footer .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }

      footer a {
        color: #fff;
        text-decoration: none;
        margin: 0 10px;
        font-size: 0.85rem;
      }

      footer a:hover {
        color: var(--accent-color);
      }

      .breadcrumb-item {
        transition: all 0.3s ease;
      }

      .breadcrumb-item:hover {
        transform: translateY(-2px);
      }

      .breadcrumb-item + .breadcrumb-item::before {
        content: "/";
        color: #6c757d;
      }

      .breadcrumb-item a {
        color: #0a6a6d;
        text-decoration: none;
        position: relative;
      }

      .breadcrumb-item a::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 2px;
        bottom: -2px;
        left: 0;
        background-color: #0a6a6d;
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .breadcrumb-item a:hover::after {
        transform: scaleX(1);
      }

      .breadcrumb-item.active {
        font-weight: bold;
      }

      .breadcrumb {
        margin-top: 10px;
        margin-bottom: -10px;
      }

      @media (max-width: 992px) {
        .blog-content,
        .related-articles {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }

      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.75rem;
        }

        .blog-content {
          padding: 1.5rem;
        }

        .blog-image {
          height: 250px;
        }

        .navbar a {
          margin: 0 10px;
        }

        .social-media a {
          font-size: 1.5rem;
          margin: 0 5px;
        }
      }

      @media (max-width: 480px) {
        body {
          font-size: 13px;
        }

        .blog-title {
          font-size: 1.5rem;
        }

        .blog-content {
          padding: 1rem;
        }

        .blog-image {
            width: 400px; /* Fixed width for the image */
    height: 300px;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .navbar a {
          margin: 0 5px;
        }

        .social-media a {
          font-size: 1.25rem;
          /* margin: 0 3px; */
          margin-left: 20px;
        }

        .breadcrumb-item {
          font-size: 0.9rem;
        }
      }
    </style>
  </head>

  <body>

    <header>
      <nav class="navbar">
        <div class="navbar-brand text-white">
          <a href="/blogs/p">Blog</a>
        </div>
        <div class="d-flex justify-content-end align-items-center">
          <div class="navbar-menu">
            <a href="/">Home</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/library-list/">Library List</a>
          </div>
          <div class="navbar-menu">
            <a href="/membership/plans/">Pricing</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/login/" class="btn btn-outline-info">Login</a>
          </div>
          <div class="navbar-menu">
            <a href="/librarian/signup/" class="btn btn-info">Signup</a>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-fluid">
      <div class="content-wrapper">
        <article class="blog-content">
          <nav aria-label="breadcrumb" class="mt-4">
            <ol class="breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">
              <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item">
                  <span itemprop="name">Home</span>
                </a>
                <meta itemprop="position" content="1" />
              </li>
              <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                <a href="/blogs/p" itemprop="item">
                  <span itemprop="name">Blogs</span>
                </a>
                <meta itemprop="position" content="2" />
              </li>
              <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                <a href="/blogs/p/cat/{{ blog.category }}/" itemprop="item">
                  <span itemprop="name">{{ blog.category }}</span>
                </a>
                <meta itemprop="position" content="3" />
              </li>
              <li class="breadcrumb-item active" aria-current="page" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                <span itemprop="name">{{ blog.title }}</span>
                <meta itemprop="position" content="4" />
              </li>
            </ol>
          </nav>
          <div class="d-flex justify-content-between border-bottom pt-4">
            <a
              href="https://librainian.com/"
              class="text-decoration-none text-dark"
              >Librainian.com</a
            >
            <span>Published on: {{ blog.published_date|date }}</span>
          </div>
          <div class="blog-header">
            <h1 class="blog-title">{{ blog.title }}</h1>
          </div>
          <p class="mt-2 mb-2 text-muted blog_description">
            <em>{{ blog.description }}</em>
          </p>
          <div class="d-flex justify-content-between align-items-center gap-3 mb-2">
            <div class="col-12 col-md-6">Updated On: {{ blog.updated_at|date }}</div>
            <div class="col-12 col-md-6">Author: {{ blog.author.user.first_name }} {{ blog.author.user.last_name }}</div>
          </div>
          <blockquote class="blog-description">
            {{ blog.introduction }}
          </blockquote>
          <div style="text-align: center">
            <!-- Centering div -->
            <img
              src="{{ blog.image.url }}"
              class="blog-image"
              alt="{{ blog.title }}"
              loading="lazy"
            />
          </div>
          <div class="blog-body">{{ blog.content|safe }}</div>
        </article>

        <aside class="related-articles mt-3">
          <h3>Related Articles</h3>
          <div id="relatedArticlesContainer">
            {% for blog in blogs %}
            <div class="related-card" data-entry="{{ forloop.counter }}">
              <a href="/blogs/p/{{blog.slug}}/" class="related-card">
                <div class="related-card-content">
                  <h4 class="related-card-title">{{ blog.title }}</h4>
                  <p class="related-card-date">
                    Published: {{ blog.date_created }}
                  </p>
                  <p class="blog-description2">
                    {{ blog.description|truncatewords:20|safe }}
                  </p>
                </div>
              </a>
            </div>
            {% if forloop.counter|divisibleby:10 and not forloop.last %}
          </div>
          <div id="relatedArticlesContainer" style="display: none">
            {% endif %} {% endfor %}
          </div>
          <nav aria-label="Related articles pagination">
            <ul class="pagination" id="relatedArticlesPagination"></ul>
          </nav>
        </aside>
      </div>
    </main>

    <div class="social-media">
      <a href="https://www.facebook.com/profile.php?id=61562707884730"
        ><i class="fab fa-facebook-f"></i
      ></a>
      <a href="https://x.com/Librainian_app?t=ge9yi-sL_8SFXeWGZS9apQ&s=09"
        ><i class="fab fa-twitter"></i
      ></a>
      <a href="https://www.instagram.com/librainian.app/"
        ><i class="fab fa-instagram"></i
      ></a>
      <a href="https://www.linkedin.com/company/pinak-venture/"
        ><i class="fab fa-linkedin-in"></i
      ></a>
    </div>

    <footer>
      <div class="container">
        <div>&copy; 2024 Librainian. All rights reserved.</div>
        <div>
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
          <a href="#">Contact Us</a>
        </div>
      </div>
    </footer>

    <!-- Bootstrap 5.0.0 JavaScript Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8" crossorigin="anonymous"></script>

    <script>
      // Bootstrap 5.0.0 initialization check
      document.addEventListener('DOMContentLoaded', function() {
        if (typeof bootstrap !== 'undefined') {
          console.log('Bootstrap 5.0.0 loaded successfully for blog display');
          console.log('Bootstrap version:', bootstrap.Modal.VERSION || 'Version not available');
        } else {
          console.error('Bootstrap is not loaded!');
        }
      });
    </script>

    <script>
      let currentPage = 1;
      const entriesPerPage = 10;

      function showPage(page) {
        const containers = document.querySelectorAll(
          "#relatedArticlesContainer"
        );
        containers.forEach((container, index) => {
          if (index === page - 1) {
            container.style.display = "block";
          } else {
            container.style.display = "none";
          }
        });
      }

      function updatePagination() {
        const containers = document.querySelectorAll(
          "#relatedArticlesContainer"
        );
        const totalPages = containers.length;
        const paginationElement = document.getElementById(
          "relatedArticlesPagination"
        );
        paginationElement.innerHTML = "";

        for (let i = 1; i <= totalPages; i++) {
          const li = document.createElement("li");
          li.className = `page-item ${i === currentPage ? "active" : ""}`;
          li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
          paginationElement.appendChild(li);
        }
      }

      document
        .getElementById("relatedArticlesPagination")
        .addEventListener("click", (e) => {
          e.preventDefault();
          if (e.target.tagName === "A") {
            currentPage = parseInt(e.target.dataset.page);
            showPage(currentPage);
            updatePagination();
          }
        });

      // Initial setup
      showPage(currentPage);
      updatePagination();
    </script>
    <script>
      window.addEventListener("load", function () {
          const path = window.location.pathname; // Get current page path
          let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

          // Increment count for the current path
          pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

          // Store updated data back to localStorage
          localStorage.setItem("page_data", JSON.stringify(pageData));
        });

        // Function to send page view data
        function sendPageData() {
          const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

          if (Object.keys(pageData).length > 0) {
            fetch(location.origin + "/librarian/track-page-view/", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": "{{ csrf_token }}",
              },
              body: JSON.stringify(pageData),
            })

              .then(() => {
                localStorage.removeItem("page_data");
              })
              .catch((error) => console.error("Error sending page data:", error));
              localStorage.removeItem("page_data");
          } else {

            console.log("No page data to send");
          }
        }

        // Send data every 10 seconds
        setInterval(sendPageData, 10000);
  </script>
  </body>
</html>
