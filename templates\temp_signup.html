<!DOCTYPE html>
<html lang="en">

<head>
    {% load bootstrap5 %}
    {% bootstrap_css %}
    {% bootstrap_javascript %}
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Page</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tiny5&display=swap" rel="stylesheet">

         <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }

        body, html {
            height: 100%;
            margin: 0;
            font-family: Arial, Helvetica, sans-serif;
        }

        .bg-image {
            background-image: url('/static/img/Dans une bibliothèque lumineuse.jpg');
            filter: blur(10px);
            -webkit-filter: blur(10px);
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            position: fixed;
            width: 100%;
            z-index: -1;
        }

        .container {
            position: relative;
            z-index: 1;
        }
        .valid {
            color: green;
        }
        .invalid {
            color: red;
        }
    </style>
</head>

<body>
<!-- Google Tag Manager (noscript) -->

    <div class="bg-image"></div>


    <section class="vh-100 d-flex align-items-center justify-content-center">
        <div class="container mt-5">
            <div class="row d-flex justify-content-center align-items-center h-100">
                <div class="col">
                    <div class="card card-registration my-4">
                        <div class="row g-0">
                            <div class="col-xl-6">
                                <img src="/static/img/Dans une bibliothèque lumineuse.jpg" alt="Sample photo"
                                    class="img-fluid" loading="lazy"
                                    style="border-top-left-radius: .25rem; border-bottom-left-radius: .25rem; height:100%" />
                            </div>
                            <div class="col-xl-6">
                                <div class="card-body p-md-5 text-black">
                                    <form>
                                        <div class="d-flex justify-content-center">
                                            <span class="h1 fw-bold mb-0 mt-3" style="font-family: 'Tiny5', sans-serif; letter-spacing: 5px;">LIBRARIAN</span>
                                        </div>

                                        <h3 class="mb-4 text-uppercase text-center">Signup</h3>
                                    
                                          

                                        <div class="row">
                                            <div class="col-md-6 mb-4">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="text" id="form3Example1m" placeholder="First name*"
                                                        class="form-control form-control-lg" name="first_name" required />
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-4">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="text" id="form3Example1n" placeholder="Last name*"
                                                        class="form-control form-control-lg" name="last_name" required />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div data-mdb-input-init class="form-outline mb-4">
                                                <input type="text" id="form3Example8" placeholder="Username*"
                                                    class="form-control form-control-lg" name="username" required />
                                            </div>
                                        </div>

      
                                        <div class="row">
                                            <div data-mdb-input-init class="form-outline mb-4">
                                                <input type="text" id="form3Example8" placeholder="Library Name*"
                                                    class="form-control form-control-lg" name="libraryname" required />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-4">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="email" id="form3Example8" placeholder="Email*"
                                                        class="form-control form-control-lg" name="email" required />
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-4">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="number" id="form3Example8" placeholder="Phone Number*"
                                                        class="form-control form-control-lg" name="phone" required />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div data-mdb-input-init class="form-outline mb-4">
                                                <input type="text" id="form3Example8" placeholder="Address*"
                                                    class="form-control form-control-lg" name="address" required />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="input-group mb-4">
                                                <input type="password" id="form3Example9" placeholder="Password*" class="form-control form-control-lg">
                                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <small id="passwordHelp" class="form-text"></small>
                                        </div>

                                        <div class="row">
                                            <div class="input-group mb-4">
                                                <input type="password" id="form3Example91" placeholder="Confirm Password*" class="form-control form-control-lg">
                                                <button class="btn btn-outline-secondary" type="button" id="togglePassword1">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <small id="confirmPasswordHelp" class="form-text"></small>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="" id="rememberMe">
                                                <label class="form-check-label" for="rememberMe">
                                                    Remember me
                                                </label>
                                            </div>
                                        </div>

                                        <div class="pt-1 mb-3">
                                            <button data-mdb-button-init data-mdb-ripple-init class="btn btn-dark btn-lg btn-block" type="submit" id="registerButton">Register</button>

                                        </div>

                                        <p class="mb-0 pb-lg-2 mt-3" style="color: #393f81; font-size: 18px;">
                                            Already have an account?
                                            <a href="/{{ role }}/login" style="color: #393f81;">Login here</a>
                                        </p>
                                        <a href="#!" class="small text-muted" style="font-size: 18px;">Terms of use.</a>
                                        <a href="#!" class="small text-muted" style="font-size: 18px;">Privacy policy</a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

  

    <script>
        document.getElementById('form3Example9').addEventListener('input', validatePassword);
        document.getElementById('form3Example91').addEventListener('input', validateConfirmPassword);

        function validatePassword() {
            const password = document.getElementById('form3Example9').value.trim();
            const passwordHelp = document.getElementById('passwordHelp');
            let valid = true;

            // Check if password starts with an uppercase letter
            if (!/^[A-Z]/.test(password)) {
                passwordHelp.textContent = "Password must start with an uppercase letter.";
                valid = false;
            }
            // Check if password contains a number
            else if (!/\d/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one number.";
                valid = false;
            }
            // Check if password contains a special character
            else if (!/[!@#$%^&*]/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one special character (!@#$%^&*).";
                valid = false;
            }
            // Check if password length is at least 8 characters
            else if (password.length < 8) {
                passwordHelp.textContent = "Password must be at least 8 characters long.";
                valid = false;
            }
            // Password is valid
            else {
                passwordHelp.textContent = "Password is valid.";
                valid = true;
            }

            // Apply styling based on validation result
            if (valid) {
                passwordHelp.classList.add('valid');
                passwordHelp.classList.remove('invalid');
            } else {
                passwordHelp.classList.add('invalid');
                passwordHelp.classList.remove('valid');
            }
        }

        function validateConfirmPassword() {
            const password = document.getElementById('form3Example9').value.trim();
            const confirmPassword = document.getElementById('form3Example91').value.trim();
            const confirmPasswordHelp = document.getElementById('confirmPasswordHelp');

            if (password === confirmPassword && confirmPassword.length > 0) {
                confirmPasswordHelp.textContent = "Passwords match.";
                confirmPasswordHelp.classList.add('valid');
                confirmPasswordHelp.classList.remove('invalid');
            } else {
                confirmPasswordHelp.textContent = "Passwords do not match.";
                confirmPasswordHelp.classList.add('invalid');
                confirmPasswordHelp.classList.remove('valid');
            }
        }

        document.getElementById('togglePassword').addEventListener('click', function () {
            const passwordField = document.getElementById('form3Example9');
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });

        document.getElementById('togglePassword1').addEventListener('click', function () {
            const confirmPasswordField = document.getElementById('form3Example91');
            const type = confirmPasswordField.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordField.setAttribute('type', type);
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });
    </script>
</body>

</html>
