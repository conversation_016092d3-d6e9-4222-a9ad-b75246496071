<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="robots" content="index,follow">
    <meta name="keywords" content="Help page, FAQs, support, library app, productivity">
    <meta name="description"
        content="Find answers to common questions or contact us for support. We are here to assist you with any issues you encounter.">
    <link rel="canonical" href="https://librainian.com/help">
    <meta property="og:title" content="Help & Support">
    <meta property="og:description"
        content="Get the help you need for your library app with our FAQs and support ticket system.">
    <meta property="og:url" content="https://www.librainian.com/help">
    <meta property="og:image" content="https://img.freepik.com/free-vector/hands-people-holding-help-placards-persons-with-signs-asking-help-donations-flat-vector-illustration-support-assistance-charity-concept-banner-website-design-landing-web-page_74855-26040.jpg">
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-01-01">
    <meta itemprop="dateModified" content="2024-01-01">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Help & Support">
    <meta name="twitter:description"
        content="Need assistance? Visit our Help & Support page to find answers or submit a ticket.">
    <meta name="twitter:image" content="https://img.freepik.com/free-vector/hands-people-holding-help-placards-persons-with-signs-asking-help-donations-flat-vector-illustration-support-assistance-charity-concept-banner-website-design-landing-web-page_74855-26040.jpg">

     
    <title>Help</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">



    <style>
        body {
            -webkit-user-select: none;
            /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }


        .footer {
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;
        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;
        }

        h2 {
            font-size: 2rem;
        }

        /* Mobile view (up to 480px width) */
        @media screen and (max-width: 480px) {
            p {
                font-size: 16px;
                /* Adjust to a smaller font size */
            }

            h2 {
                font-size: 20px;
            }

            h1 {
                font-size: 24px;
            }

            .lead {
                font-size: 16px;
            }
        }

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

            h2 {
                font-size: 24px;
            }

            h1 {
                font-size: 30px;
            }

            .lead {
                font-size: 20px;
            }
        }
    </style>

</head>

<body>


    <!-- Sidebar -->

    {% include "sidebar.html" %}

    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 25rem;">
            DASHBOARD / HELP & SUPPORT
        </div>
    </div>
    <div class="container mt-4 mb-5">
        <div class="text-center mb-4">
            <h1>Help & Support</h1>
            <p class="lead">Find answers to your questions or contact us for further assistance.</p>
        </div>
        <div class="row">
            <!-- FAQ Section -->
            <div class="col-md-7">

                <div class="mb-4">
                    <input type="text" id="searchInput" class="form-control" placeholder="Search FAQs">
                </div>

                <div class="accordion" id="faqAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne"
                            style="background: #294282 !important; cursor: pointer;" data-toggle="collapse"
                            data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            <h2 class="mb-0">
                                <button class="btn" style="color: white !important; text-decoration: none !important"
                                    type="button">
                                    How do I reset my password?
                                </button>
                            </h2>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                            data-parent="#faqAccordion">
                            <div class="card-body">
                                To reset your password, go to the login page and click on "Forgot Password". Follow the
                                instructions sent to your registered email address.
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header" id="headingTwo"
                            style="background: #294282 !important; cursor: pointer;" data-toggle="collapse"
                            data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            <h2 class="mb-0">
                                <button class="btn collapsed"
                                    style="color: white !important; text-decoration: none !important" type="button">
                                    How can I update my profile info?
                                </button>
                            </h2>
                        </div>
                        <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#faqAccordion">
                            <div class="card-body">
                                To update your profile information, go to the "Profile" section in your dashboard and
                                make
                                the necessary changes. Don't forget to save your changes.
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header" id="headingThree"
                            style="background: #294282 !important; cursor: pointer;" data-toggle="collapse"
                            data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            <h2 class="mb-0">
                                <button class="btn collapsed"
                                    style="color: white !important; text-decoration: none !important" type="button">
                                    How do I contact support?
                                </button>
                            </h2>
                        </div>
                        <div id="collapseThree" class="collapse" aria-labelledby="headingThree"
                            data-parent="#faqAccordion">
                            <div class="card-body">
                                You can contact support by filling out the contact form below or by emailing us at
                                <EMAIL>.
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header" id="headingFour"
                            style="background: #294282 !important; cursor: pointer;" data-toggle="collapse"
                            data-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                            <h2 class="mb-0">
                                <button class="btn collapsed"
                                    style="color: white !important; text-decoration: none !important" type="button">
                                    How do I cancel my subscription?
                                </button>
                            </h2>
                        </div>
                        <div id="collapseFour" class="collapse" aria-labelledby="headingFour"
                            data-parent="#faqAccordion">
                            <div class="card-body">
                                To cancel your subscription, go to the "Subscription" section in your account settings
                                and
                                follow the cancellation instructions provided.
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header" id="headingFive"
                            style="background: #294282 !important; cursor: pointer;" data-toggle="collapse"
                            data-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                            <h2 class="mb-0">
                                <button class="btn collapsed"
                                    style="color: white !important; text-decoration: none !important" type="button">
                                    How can I change my email address?
                                </button>
                            </h2>
                        </div>
                        <div id="collapseFive" class="collapse" aria-labelledby="headingFive"
                            data-parent="#faqAccordion">
                            <div class="card-body">
                                To change your email address, go to the "Account" or "Profile" section in your dashboard
                                and
                                update your email information.
                            </div>
                        </div>
                    </div>

                    <!-- Add more FAQ items as needed -->
                </div>
            </div>
            <div class="col-md-5">
                <div style="max-width: 500px; width: 100%;">
                    <h2 class="mb-4">Generate Ticket</h2>
                    {% if messages %}
                    <div id="messageContainer">
                        {% for message in messages %}
                        {% if message.tags == 'success' %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {% elif message.tags == 'error' %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                {% elif message.tags == 'warning' %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    {% elif message.tags == 'info' %}
                                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                                        {% else %}
                                        <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                            {% endif %}
                                            {{ message }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <form method="post" class="p-3"
                                        style="background: #cee3e0 !important; border-radius: 1rem;">
                                        {% csrf_token %}
                                        <div class="form-group">
                                            <label for="issueType" style="color: #294282 !important;">Select Issue
                                                Type:</label>
                                            <select class="form-control" id="issueType" name="issueType" required>
                                                <option value="">Choose an option</option>
                                                <option value="frontend">Frontend</option>
                                                <option value="backend">Backend</option>
                                                <option value="fullstack">Fullstack</option>
                                                <option value="design">Design</option>
                                                <option value="devops">DevOps</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="title" style="color: #294282 !important;">Title:</label>
                                            <textarea class="form-control" name="title" rows="2" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="description" style="color: #294282 !important;">Issue
                                                Description:</label>
                                            <textarea class="form-control" name="description" rows="5"
                                                required></textarea>
                                        </div>
                                        <div class="text-center">
                                            <button type="submit" class="btn"
                                                style="background: #294282; color: white;">Submit Ticket</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- footer -->
                        <div class="footer">
                            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy"
                                style="filter: invert(0.5) brightness(0);">
                            <!-- <p>Developed with passion by Librainian</p> -->
                        </div>
                    </div>

                    <!-- JavaScript dependencies -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
                    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
                    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


                     


                    <script>
                        // Show success message and hide after 10 seconds
                        document.addEventListener('DOMContentLoaded', function () {
                            var alerts = document.querySelectorAll('.alert');
                            alerts.forEach(function (alert) {
                                setTimeout(function () {
                                    alert.style.transition = 'opacity 1s';
                                    alert.style.opacity = '0';
                                    setTimeout(function () {
                                        alert.style.display = 'none';
                                    }, 1000);
                                }, 10000);
                            });
                        });
                    </script>

                    <script>
                        $(document).ready(function () {
                            $("#searchInput").on("keyup", function () {
                                var value = $(this).val().toLowerCase();
                                $("#faqAccordion .card").filter(function () {
                                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                                });
                            });
                        });
                    </script>
                    <script>
                        function updateDateTime() {
                            const now = new Date();
                            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
                            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

                            document.getElementById('date').textContent = 'Date: ' + date;
                            document.getElementById('time').textContent = 'Time: ' + time;
                        }

                        // Update the date and time on page load
                        updateDateTime();

                        // Update the date and time every minute
                        setInterval(updateDateTime, 60000);
                    </script>




                    <script>
                        // Get the menu icon and submenu elements
                        const menuIcon = document.getElementById('menu-icon');
                        const submenu = document.getElementById('submenu');

                        // Add click event listener to the menu icon
                        menuIcon.addEventListener('click', function () {
                            submenu.classList.toggle('show');
                        });
                    </script>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            var menuIcon = document.getElementById('menu-icon');
                            var submenu = document.getElementById('submenu');

                            // Toggle submenu visibility on menu icon click
                            menuIcon.addEventListener('click', function () {
                                if (submenu.style.display === 'block') {
                                    submenu.style.display = 'none';
                                } else {
                                    submenu.style.display = 'block';
                                }
                            });
                        });

                    </script>
                     
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            var footerSearch = document.getElementById('footer-search');
                            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

                            footerSearch.addEventListener('click', function () {
                                exampleModal.show();
                            });
                        });
                    </script>

                    <link rel="stylesheet"
                        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

</body>

</html>