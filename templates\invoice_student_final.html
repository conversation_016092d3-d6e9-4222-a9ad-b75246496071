<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex, nofollow" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
      <!-- Bootstrap CSS -->
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <title>Student Profile</title>

         <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>
        /* General body styling */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
            background-color: #f4f4f9;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        /* Container styling */
        .container {
            max-width: 800px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* Header styling */
        .header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }

        .header img {
            width: 200px;
            margin-bottom: 10px;
        }

        .header h1 {
            margin: 10px 0;
            font-size: 28px;
            color: #333;
        }

        /* Profile picture styling */
        .profile-picture {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #007bff;
            margin: 0 auto 20px auto;
            display: block;
        }

        /* Info sections styling */
        .info-section {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 1rem;
            border-radius: 10px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: background-color 0.3s;
        }

        /* .info-section:hover {
            background-color: #eaf0f8;
        } */

        .info-item {
            flex: 1 1 100%;
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .info-title {
            font-weight: bold;
            color: #294282;
            font-size: 18px;
        }

        .info-section h2 {
            text-align: center;
            width: 100%;
            margin-bottom: 20px;
            color: #333;
        }

        /* Payment Details styling */
        .payment-details {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .payment-details h2 {
            text-align: center;
            width: 100%;
            margin-bottom: 20px;
            color: #333;
        }
        .payment-details table {
            width: 100%;
            border-collapse: collapse;
        }

        .payment-details th,
        .payment-details td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .payment-details th {
            background-color: #98989e;
            color: white;
            font-weight: normal;
        }

        .payment-details tr:last-child td {
            border-bottom: none;
        }

        /* Centering text */
        .text-center {
            display: flex;
            justify-content: center;
            /* Center horizontally */
            align-items: center;
            /* Center vertically */
            height: 10vh;
            /* Adjust as needed */
        }

        /* Download button styling */
        .btn-download {
            display: inline-block;
            padding: 12px 25px;
            margin-right: 25px;
            color: #fff;
            background-color: #294282;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-download:hover {
            color: #fff;
            background-color: #28345a;
        }

        /* Footer styling */
        .footer {
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;
        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .footer img {
                width: 60%;
            }
            .btn-download{
                width: 100%;
            }
        }

        @media (max-width: 600px) {

            .container,
            .footer {
                padding: 15px;
            }

            .info-item {
                flex: 1 1 100%;
            }

            .btn-download {
                padding: 10px 20px;
            }
        }

        /* Advertisement Section Styling */
        .advertisement-section {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .advertisement-section h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
        }

        .advertisement-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .advertisement-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .advertisement-image {
            flex-shrink: 0;
            margin-right: 15px;
        }

        .advertisement-image img {
            max-width: 150px;
            /* Set maximum width */
            max-height: 100px;
            /* Set maximum height */
            width: auto;
            /* Maintain aspect ratio */
            height: auto;
            /* Maintain aspect ratio */
            display: block;
        }

        .advertisement-content {
            flex: 1;
        }

        .advertisement-content p {
            margin: 0;
            color: #666;
        }
        .terms-btn {
            background: none;
            border: none;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            color: #333;
        }

        .terms-btn span {
            transition: transform 0.3s ease;
        }
        .terms-btn[aria-expanded="true"] span {
            transform: rotate(180deg);
        }
    </style>
</head>

<body>    

    <div class="container" style="background-color: #9bc6bf;">
        <!-- Header -->
        <div class="header">
            <img src="{% if invoice.student.librarian.image %}{{ invoice.student.librarian.image.url }}{% else %}/static/img/librainian-logo-white-trans.png{% endif %}"
                alt="Library Logo" class="img-fluid" style="width: 150px;" loading="lazy">

        </div>

        <div class="info-section">
            <div class="flex flex-md-row flex-column justify-content-md-between fs-5" style="font-weight: 500;">
                <div>Amount Paid</div>
                <div>{{ invoice.total_amount }}</div>
            </div>
            <div class="flex justify-content-between fs-5" style="font-weight: 500;">
                <div>{{invoice.issue_date}}</div>
                <div>1 items</div>
            </div>
        </div>
        <div class="info-section flex-column justify-content-center align-items-center pt-4" style="font-weight: 600;">
            <p class="text-center"><strong>Thank you for contributing to the environmental preservation <br> initiativeby opting for paperless billing.<br> Dear {{invoice.student.name}}</strong></p> 
        </div>

        <!-- Library Information -->
        <div class="info-section">
            <h2>Library Details</h2>
            <div class="info-item">
                <div class="info-title">Library Name:</div>
                <div>{{invoice.student.librarian.library_name}}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Address:</div>
                <div>{{invoice.student.librarian.librarian_address}}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Website:</div>
                <div><a href="{{library_website}}" target="_blank">www.librarian.com</a></div>
            </div>
            <div class="info-item">
                <div class="info-title">Contact:</div>
                <div>{{invoice.student.librarian.librarian_phone_num}}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Email:</div>
                <div>{{invoice.student.librarian.user.email}}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Library Map:</div>
                <div><a href="{{invoice.student.librarian.google_map_url}}">{{invoice.student.librarian.google_map_url}}</a></div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="info-section">
            <h2>Student Details</h2>
            <div class="info-item">
                <div class="info-title">Registration ID:</div>
                <div>{{ invoice.student.unique_id }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Student Name:</div>
                <div>{{ invoice.student.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Registration Date:</div>
                <div>{{ invoice.student.registration_date }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Student Address:</div>
                <div>{{ invoice.student.locality }}, {{ invoice.student.city }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Mobile:</div>
                <div>{{ invoice.student.mobile }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Email:</div>
                <div>{{ invoice.student.email }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Registration Fee:</div>
                <div>{{ invoice.student.registration_fee }}</div>
            </div>
            <div class="info-item">
                <div class="info-title">Registration Fee Status</div>
                <div> {{ registration_fee.is_paid|yesno:"Paid,Unpaid" }}</div>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="payment-details mb-4">
            <h2>Payment Details</h2>
            <table>
                <tr>
                    <th>Invoice Number</th>
                    <td>{{ invoice.invoice_id }}</td>
                </tr>
                <tr>
                    <th>Invoice Date</th>
                    <td>{{ invoice.issue_date }}</td>
                </tr>
                <tr>
                    <th>Payment Date</th>
                    <td>{{ invoice.issue_date }}</td>
                </tr>
                <tr>
                    <th>Next Due Date</th>
                    <td>{{ invoice.due_date }}</td>
                </tr>
                <tr>
                    <th>Month</th>
                    <td>
                        {% for month in invoice.months.all %}
                        {{ month.name }}{% if not forloop.last %} | {% endif %}
                        {% endfor %}
                    </td>
                </tr>
                <tr>
                    <th>Shift</th>
                    <td>
                        {% for shift in invoice.shift.all %}
                        {{ shift.name }}- ₹{{ shift.price }}{% if not forloop.last %} | {% endif %}
                        {% endfor %}
                    </td>
                </tr>
                <tr>
                    <th>Seat Booked</th>
                    <td>
                        {% for seat in seats %}
                        {{ seat.seat.seat_number }}- {{ seat.seat.shift }}{% if not forloop.last %} | {% endif %}
                        {% endfor %}
                    </td>
                </tr>
                <tr>
                    <th>Discount</th>
                    <td>₹{{ invoice.discount_amount }}</td>
                </tr>
                <tr>
                    <th>Total Amount</th>
                    <td>₹{{ invoice.total_amount }}</td>
                </tr>
                <tr>
                    <th>Net Amount</th>
                    <td>₹{{ invoice.total_amount }}</td>
                </tr>
                <tr>
                    <th>Mode of Payment</th>
                    <td>{{ invoice.mode_pay }}</td>
                </tr>
            </table>
        </div>

        <div class="info-section flex-column justify-content-center align-items-center" style="font-weight: 600;">
            <p class="fs-4"><strong>Corporate Address</strong></p>
            <p>{{invoice.student.librarian.librarian_address}}</p>
        </div>
        <div class="info-section flex-column justify-content-center align-items-center" style="font-weight: 600;">
            <button class="terms-btn" data-bs-toggle="collapse" data-bs-target="#termsList" aria-expanded="false">
                Terms & Conditions <span class="">🔻</span>
            </button>
            <div id="termsList" class="collapse mt-3">
            <ol class="text-start text-muted d-flex flex-column gap-2" style="font-size: 0.9rem;">
                <li>Fee payment is not transferable in nature.</li>
                <li>In case of unavailability or refund, library's terms and condition is bounding.</li>
                <li>In case or transfer or refund, library hold right to decide the amount or duration of transferable</li>
                <li>Late Fee, registration Fee are not refundable.</li>
                <li>One must obey local library's rules.</li>
                <li>Please refer user instructions.</li>
                <li>For security reasons, please carry your e-bill to be display on demand.</li>
                <li>Please check all the details at the time of payment. No complaints will be entertained later.</li>
                <li>No cash refund.</li>
                <li>Library are open all 7 days of the week.</li>
            </ol>
            </div>
        </div>

        <!-- Download Button -->
        <div class="text-center">
            <a href="#" class="btn-download small" style="text-decoration: none !important;" onclick="window.print(); return false;">Download Profile as PDF</a>
            {% if user.is_authenticated %}
            {% if role == "librarian" or role == "sublibrarian" %}
            <a href="/students/" class="btn-download small" style="text-decoration: none !important;" type="submit">Student Dashboard</a>
            {% endif %}
            {% endif %}
        </div>
        
    </div>

    <!-- Footer -->
    <div class="footer">
        <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
    </div>


</body>

</html>