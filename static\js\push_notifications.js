
        // Your web app's Firebase configuration
        // const firebaseConfig = {
        //     apiKey: "AIzaSyBZZvIp2gXXdQk5kpLnIQGkdgQgP7Vz4aw",
        //     authDomain: "librainian-d44e1.firebaseapp.com",
        //     projectId: "librainian-d44e1",
        //     storageBucket: "librainian-d44e1.firebasestorage.app",
        //     messagingSenderId: "862158269429",
        //     appId: "1:862158269429:web:6a0ac81f4136188411cebc",
        //     measurementId: "G-3L2X8JMGF2",
        // };

        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

       

        // Initialize Firebase
        const app = firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Automatically request notification permission and register device token
        document.addEventListener('DOMContentLoaded', function() {
            const notificationBtn = document.getElementById('notificationBtn');
            notificationBtn.style.display = 'none'; // Hide the button since we will auto-register

            if ('Notification' in window) {
                requestAndShowNotification();
            } else {
                console.log('This browser does not support notifications.');
            }
        });

        function requestAndShowNotification() {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    showNotification();
                    registerDeviceToken();
                } else {
                    console.log('Notification permission denied');
                }
            });
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            messaging.getToken({ vapidKey: 'BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY	' }).then((currentToken) => {
                if (currentToken) {
                    const deviceType = 'web'; // Change this based on the actual device type
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    console.log('No registration token available. Request permission to generate one.');
                }
            }).catch((err) => {
                console.log('An error occurred while retrieving token. ', err);
            });
        }

        function saveDeviceToken(token, deviceType) {
            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken') // Get CSRF token for security
                },
                body: `token=${token}&device_type=${deviceType}`
            })
            .then(response => response.json())
            .then(data => {
                console.log(data.message);
                console.log('Device Token:', token); // Log the device token to the console
            })
            .catch(error => {
                console.error('Error saving device token:', error);
            });
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker
                .register('/firebase-messaging-sw.js')
                .then(function(registration) {
                    console.log('Service Worker registered with scope:', registration.scope);
                })
                .catch(function(err) {
                    console.log('Service Worker registration failed:', err);
                });
        }
        