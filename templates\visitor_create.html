<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Visitors</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
    <!-- Google Tag Manager -->




        <!-- disable Print Screen for Windows -->



        <!-- disable print screen for mac -->



        <!-- disabling print screen for Linux -->



        <!-- disabling inspection tool -->


    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }


        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            /* Full height of the viewport */
            overflow-y: auto;
            /* Enable vertical scrolling */
            overflow-x: hidden;
            /* Hide horizontal overflow */

            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar h4 {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #495057;
        }

        .sidebar a {
            color: #fff;
            padding: 15px 10px;
            padding-left: 25px;
            display: block;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .sidebar a:hover,
        .sidebar a.active {
            background-color: #495057;
            border-left: 3px solid #007bff;

        }

        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        /* .form-control {
            border: none;
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        } */

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }
    @media only screen and (max-width: 767px) {
            .small_p p{
            font-size: 12px !important;
        }
        }
        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }

        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }

        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        /* Header styling */
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 10px 0;
            font-size: 28px;
            color: #333;
        }
        .header::after {
            content: '';
            display: block;
            width: 50%;
            height: 3px;
            background-color: #007bff;
            margin: 15px auto 0;
        }
        /* Form styling */
        form {
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #007bff;
        }
        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="tel"],
        .form-group select,
        .form-group textarea,
        .form-group input[type="date"] {
            width: calc(100% - 20px);
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
            color: #333;
        }
        .form-group textarea {
            height: 150px;
            resize: vertical;
        }
        .form-check-label {
            padding-left: 0px;
        }
        /* Button styling */
        .btn-create-visitor, .btn-back {
            display: inline-block;
            padding: 10px 20px;
            margin-right: 10px;
            color: #fff;
            background-color: #28a745;
            border: none;
            border-radius: 5px;
            text-align: center;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .btn-back {
            background-color: #007bff;
        }
        .btn-create-visitor:hover {
            background-color: #218838;
        }
        .btn-back:hover {
            background-color: #0069d9;
        }
        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .form-group input[type="text"],
            .form-group input[type="email"],
            .form-group input[type="tel"],
            .form-group select,
            .form-group textarea,
            .form-group input[type="date"] {
                font-size: 0.9em;
            }
            .btn-create-visitor, .btn-back {
                font-size: 0.9em;
            }
        }
        /* Footer styling */
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }
            .btn-create-visitor{
                margin-bottom: 20px;
            }
        }

        #notificationBtn {
            display: none;
        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }
    </style>
</head>

<body>

    <!-- Sidebar -->
{% include "sidebar.html" %}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
        </div>
    </div>
</div>

        <div class="container">

            <!-- Header -->
            <div class="header">
                <h1>{% if visitor %}Edit Visitor{% else %}Create Visitor{% endif %}</h1>
            </div>

            <!-- Visitor Details Form -->
            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="name"><i class="fas fa-user"></i> Full Name</label>
                    <input type="text" id="name" name="name" placeholder="Enter visitor's full name" value="{% if visitor %}{{ visitor.name }}{% endif %}" required pattern="[A-Za-z ]+">
                </div>

                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="email" name="email" placeholder="Enter visitor's email" value="{% if visitor %}{{ visitor.email }}{% endif %}" required>
                </div>

                <div class="form-group">
                    <label for="phone"><i class="fas fa-mobile-alt"></i> Mobile Number</label>
                    <input type="tel" id="phone" name="phone" placeholder="Enter visitor's mobile number" value="{% if visitor %}{{ visitor.contact }}{% endif %}" maxlength="10" pattern="[0-9]{10}" title="Please enter a valid 10-digit mobile number." required>
                </div>

                <div class="form-group">
                    <label for="shifts"><i class="far fa-clock"></i> Shifts</label><br>
                    {% for shift in shifts %}
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="shift{{ shift.id }}" name="shifts" value="{{ shift.id }}"
                                   {% if shift in visitor.shift.all %}checked{% endif %}>
                            <label class="form-check-label" for="shift{{ shift.id }}">{{ shift.name }}</label>
                        </div>
                    {% endfor %}
                </div>

                <div class="form-group">
                    <label for="notes"><i class="fas fa-sticky-note"></i> Notes</label>
                    <textarea id="notes" name="note" placeholder="Enter any notes or comments">{% if visitor %}{{ visitor.notes }}{% endif %}</textarea>
                </div>

                <div class="form-group">
                    <label for="visit-date"><i class="far fa-calendar-alt"></i> Visit Date</label>
                    <input type="date"  name="date" value="{% if visitor and visitor.date %}{{ visitor.date|date:'Y-m-d' }}{% endif %}" required>
                </div>

                <div class="form-group">
                    <label for="callback-date"><i class="far fa-calendar-check"></i> Callback Date</label>
                    <input type="date"  name="callback" value="{% if visitor and visitor.callback %}{{ visitor.callback|date:'Y-m-d' }}{% endif %}">
                </div>


                <div class="form-group">
                    <label for="status"><i class="fas fa-check-circle"></i> Status</label>
                    <select id="status" name="status" class="form-control" required>
                        <option value="">Select Status</option>
                        <option value="pending" {% if visitor and visitor.status == "pending" %}selected{% endif %} >Pending</option>
                        <option value="completed" {% if visitor and visitor.status == "completed" %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if visitor and visitor.status == "cancelled" %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-create-visitor">{% if visitor %}Update Visitor{% else %}Create Visitor{% endif %}</button>
                    <a href="/visitors/" class="btn btn-back">Back to Visitor List</a>
                </div>
            </form>
        </div>

        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
            <!-- <p>Developed with passion by Librainian</p> -->
        </div>






    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Full Name Validation
            document.getElementById('name').addEventListener('input', function(e) {
                this.value = this.value.replace(/[^A-Za-z ]/g, '');
            });

            // Email Validation
            document.getElementById('email').addEventListener('input', function(e) {
                // Remove any URL-like patterns
                this.value = this.value.replace(/(?:https?:\/\/|www\.)\S+/gi, '');
            });

            // Mobile Number Validation
            document.getElementById('phone').addEventListener('input', function(e) {
                this.value = this.value.replace(/\D/g, '').slice(0, 10);
            });

            // Form Submission Validation
            document.getElementById('visitorForm').addEventListener('submit', function(e) {
                var name = document.getElementById('name').value;
                var email = document.getElementById('email').value;
                var phone = document.getElementById('phone').value;

                if (!/^[A-Za-z ]+$/.test(name)) {
                    alert('Please enter a valid name (only alphabets and spaces).');
                    e.preventDefault();
                    return false;
                }

                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    alert('Please enter a valid email address.');
                    e.preventDefault();
                    return false;
                }

                if (!/^[0-9]{10}$/.test(phone)) {
                    alert('Please enter a valid 10-digit mobile number.');
                    e.preventDefault();
                    return false;
                }
            });
        });
        </script>
    <script>

    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
         <script>
            function updateDateTime() {
                const now = new Date();
                const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
                const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

                document.getElementById('date').textContent = 'Date: ' + date;
                document.getElementById('time').textContent = 'Time: ' + time;
            }

            // Update the date and time on page load
            updateDateTime();

            // Update the date and time every minute
            setInterval(updateDateTime, 60000);
        </script>
</body>

</html>