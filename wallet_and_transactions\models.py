from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Wallet(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    balance = models.PositiveIntegerField(default=10)  # Points balance
    razorpay_order_id = models.CharField(max_length=100, default="Null")
    razorpay_payment_id = models.CharField(max_length=100, default="Null")
    razorpay_signature = models.CharField(max_length=100, default="Null")

    def __str__(self):
        return f"{self.user.username}'s Wallet"

    # def credit(self, amount, description=""):
    #     """Credits points to the wallet."""
    #     self.balance += amount
    #     self.save()
    #     Transaction.objects.create(
    #         wallet=self, amount=amount, description=description, is_credit=True
    #     )

    # def debit(self, amount, description=""):
    #     """Debits points from the wallet."""
    #     if self.balance >= amount:
    #         self.balance -= amount
    #         self.save()
    #         Transaction.objects.create(
    #             wallet=self, amount=amount, description=description, is_credit=False
    #         )
    #     else:
    #         raise ValueError("Insufficient balance")


class Transaction(models.Model):
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    amount = models.PositiveIntegerField()
    is_credit = models.BooleanField(default=False)
    is_debit = models.BooleanField(default=False)
    note = models.CharField(max_length=100)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        # type_str = "Credit" if self.is_credit else "Debit"
        return f"{self.amount} points for {self.wallet.user.username}"
