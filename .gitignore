# Django project .gitignore
# Customized for Librainian project

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Migrations (keep track of migrations in version control)
# */migrations/

# Environment and local settings
.env
.venv
env/
venv/
ENV/
myenv/
/.venv/
Library/settings.py

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Node artifact files
node_modules/
dist/

# Unit test reports
TEST*.xml
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Project specific
well-known/assetlinks.json
robots.txt
sitemap.xml
*/migrations/