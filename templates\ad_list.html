<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advertisements</title>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">


    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>
</head>
<body>


    <div class="container mt-5">
        <h1>Advertisements</h1>
        <a href="{% url 'advertisement_create' %}" class="btn btn-success mb-3">Create New Ad</a>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Subject</th>
                    <th>Amount</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for ad in ads %}
                <tr>
                    <td>{{ ad.subject }}</td>
                    <td>₹{{ ad.amount }}</td>
                    <td>{{ ad.priority }}</td>
                    <td>{{ ad.status|yesno:"Active,Inactive" }}</td>
                    <td>
                        <a href="{% url 'advertisement_detail' ad.pk %}" class="btn btn-primary btn-sm">View</a>
                        <a href="{% url 'advertisement_update' ad.pk %}" class="btn btn-warning btn-sm">Edit</a>
                        <form method="POST" action="{% url 'advertisement_delete' ad.pk %}" style="display:inline-block;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
