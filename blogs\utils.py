from django.utils.text import slugify

def generate_unique_slug(model_class, title):
    """
    Generates a unique slug for the given model_class based on the title.
    """
    slug = slugify(title)
    unique_slug = slug
    count = 1
    
    
    while model_class.objects.filter(slug=unique_slug).exists():
        unique_slug = f"{slug}-{count}"
        count += 1
        

    return unique_slug
