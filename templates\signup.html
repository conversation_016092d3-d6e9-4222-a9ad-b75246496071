<!DOCTYPE html>
<html lang="en">
<head>
    {% load socialaccount %}
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index,follow,max-image-preview:large">
    <link rel="canonical" href="https://www.librainian.com/librarian/signup/">
    <meta name="keywords" content="library registration, librarian signup, library management system, library software, library app, create library account, library management tool, digital library, library automation, library database">
    <meta name="description" content="Join Librainian - The #1 Library Management System! Register now to access exclusive features, manage your library effectively, track expenses, and streamline operations. Free trial available.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Register for Librainian - Modern Library Management System">
    <meta property="og:description" content="Sign up now to enhance your library's management and productivity with Librainian. Join 600+ libraries already using our platform!">
    <meta property="og:url" content="https://www.librainian.com/librarian/signup/">
    <meta property="og:image" content="https://www.librainian.com/static/img/Dans-une-bibliothèque-lumineuse.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Register for Librainian - Modern Library Management System">
    <meta name="twitter:description" content="Sign up now to enhance your library's management and productivity with Librainian. Join 600+ libraries already using our platform!">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/Dans-une-bibliothèque-lumineuse.jpg">

    <!-- Author and Date Info -->                                                                                                                                                                                                                                                                                                      
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-01-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <!-- Mobile App Capability -->
    <meta name="theme-color" content="#042299">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Librainian">

    <title>Register for Librainian | Modern Library Management System</title>

    <!-- Stylesheets -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ENjdO4Dr2bkBIFxQpeoY8N38y+6HrDgMX8yLvMw8e4jUHlS/z2htRrad2KbnI3eB" crossorigin="anonymous"></script>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tiny5&display=swap" rel="stylesheet">

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Register for Librainian | Modern Library Management System",
        "description": "Join Librainian - The #1 Library Management System! Register now to access exclusive features, manage your library effectively.",
        "url": "https://www.librainian.com/librarian/signup/",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Register",
                    "item": "https://www.librainian.com/librarian/signup/"
                }
            ]
        },
        "mainEntity": {
            "@type": "Service",
            "name": "Library Management System",
            "offers": {
                "@type": "Offer",
                "price": "399",
                "priceCurrency": "INR",
                "availability": "https://schema.org/InStock"
            },
            "description": "Take your library online with our all-in-one management tool. Gain control, understand performance, and forecast your growth."
        }
    }
    </script>

    <!-- Form Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Librainian Registration Form",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
            "@type": "Offer",
            "price": "399",
            "priceCurrency": "INR"
        },
        "description": "Registration form for Librainian - The #1 Library Management System",
        "potentialAction": {
            "@type": "RegisterAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://www.librainian.com/librarian/signup/",
                "inLanguage": "en-US",
                "actionPlatform": [
                    "http://schema.org/DesktopWebPlatform",
                    "http://schema.org/MobileWebPlatform"
                ]
            },
            "result": {
                "@type": "UserAccount"
            }
        }
    }
    </script>


    <style>

body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
        }
        body,
        html {
            height: 100%;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
        }

        .bg-image {
            background-image: url('/static/img/Dans-une-bibliothèque-lumineuse.jpg');
            filter: blur(10px);
            -webkit-filter: blur(10px);
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            position: fixed;
            width: 100%;
            z-index: -1;
        }

        /* .container {
            position: relative;
            z-index: 1;
        } */

        .valid {
            color: green;
        }

        .invalid {
            color: red;
        }

        #passwordHelp {
            padding-bottom: 20px;
        }



    .card {
      width: 100%;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .btn-dark a {
      text-decoration: none;
    }

    .gbtn {
      border: 1px solid #93b7ff;
      border-radius: 4px;
      background-color: #f0f0f0;
      padding: 6px 14px; /* Adjusted padding for smaller buttons */
      cursor: pointer;
      font-size: 18px; /* Smaller font size */
      color: black;
      width: 100% ;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }

    .gbtn img {
      width: 28px; /* Adjusted icon size */
      height: 28px;
      margin-right: 1rem;
      vertical-align: middle;
      background-color: rgb(255, 255, 255);
      border-radius: 50%;
      padding: 3px;
    }

    .gbtn:focus {
      outline: 2px solid #8ab5ff;
      outline-offset: 4px;
    }

    .gbtn:hover {
      background-color: #3f83f1;
      border: 1px solid #0a1d44ad;
      color: white;
      transition: 1s;
    }

    .btn-group-inline {
      display: flex;
      gap: 8px; /* Adjusted gap */
      justify-content: space-between;
    }

    a {
      color: #393f81;
      text-decoration: none;
      transition: color 0.3s, text-decoration 0.3s;
      font-size: 14px; /* Reduced font size for links */
    }

    a:hover {
      color: #1a237e; /* Increased contrast on hover */
      text-decoration: none; /* Removed underline */
    }

    .form-control{
        border-radius: 1rem;
    }

    .form-control-lg {
      font-size: 14px; /* Reduced font size for form inputs */
      padding: 0.5rem; /* Adjusted padding */
    }

    .form-outline {
      margin-bottom: 1rem;
    }
    .container {

        display: flex;
        justify-content: center;
    }


    @media only screen and (min-width: 767px) {

        .container {
            padding: 1rem;
        }

        .signup_card {
            width: 100%;
            max-width: 800px;  /* Set a max width for the card */
            margin: 0 auto;  /* Ensure it doesn't overflow */
        }

    }

    @media only screen and (min-width: 1023px) {
        .container {
            height: 125vh;
        }
    }

    </style>

</head>

<body>

    <div class="bg-image"></div>

    <section class="container">
        <div>
            <div class="signup_card d-flex">
            <div class="row d-flex justify-content-center align-items-center h-100">
                <div class="col">
                    <div class="card card-registration my-4">
                        <div class="row g-0">
                            <div class="col-md-6 d-none d-md-block">
                                <img src="/static/img/Dans-une-bibliothèque-lumineuse.jpg" alt="Sample photo"
                                class="img-fluid"
                                style="border-top-left-radius: .25rem; border-bottom-left-radius: .25rem; height:100%" />
                            </div>
                            <div class="col-md-6">
                                <div class="card-body p-md-4 text-black">
                                    <form method="post" onsubmit="return checkPasswordMatch()">
                                        {% csrf_token %}
                                        <div class="d-flex justify-content-center">
                                            <span class="h3 fw-bold mb-0"
                                                style="font-family: 'Tiny5', sans-serif; letter-spacing: 5px;">LIBRARIAN</span>
                                        </div>

                                        <h4 class="mb-2 text-uppercase text-center">{{ role }} Signup</h4>
                                        {% if messages %}
                                        {% for message in messages %}
                                        <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                            {{ message }}
                                        </div>
                                        {% endfor %}
                                        {% endif %}

                                        {% if role == "manager" %}
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <label for="library_commander">Admin Name:</label>
                                                <select id="library_commander" name="library_commander"
                                                    class="form-select form-control" required>
                                                    {% for labc in librarycommander %}
                                                    <option value = "{{ labc.id }}"
                                                    {% if labc.id == selected_library_commander_id %}selected{% endif %}>
                                                        {{ labc.user.first_name }} {{ labc.user.last_name }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        </div>
                                        {% endif %}

                                        {% if role == "librarian" %}
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <label for="manager">Manager:</label>
                                                <select id="manager" name="manager" class="form-select form-control" required>
                                                    {% for man in managers %}
                                                    <option value="{{ man.id }}" {% if man.id == selected_manager_id %}selected{% endif %}>
                                                        {{ man.user.first_name }} {{ man.user.last_name }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        </div>
                                        {% endif %}

                                        {% comment %} {% if role == "sublibrarian" %}
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <label for="librarian">Librarian:</label>
                                                <select id="librarian" name="librarian" class="form-select form-control" required>
                                                    {% for lab in librarian %}
                                                    <option value="{{ lab.id }}" {% if lab.id==selected_librarian_id
                                                        %}selected{% endif %}>
                                                        {{lab.library_name}} | {{ lab.user.first_name }} {{
                                                        lab.user.last_name }}
                                                    </option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        </div>
                                        {% endif %} {% endcomment %}

                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="text" id="form3Example1m" placeholder="First name*"
                                                        class="form-control form-control-lg" name="first_name" required
                                                        pattern="[A-Za-z ]+" title="Please enter only alphabets" />
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="text" id="form3Example1n" placeholder="Last name*"
                                                        class="form-control form-control-lg" name="last_name"
                                                        required />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <input type="text" id="form3Example8" placeholder="Username*"
                                                    class="form-control form-control-lg" name="username" required />
                                            </div>
                                        </div>
                                        </div>

                                        {% if role == "librarian" %}

                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <input type="text" id="libraryName" placeholder="Library Name*"
                                                    class="form-control form-control-lg" name="libraryname" required
                                                    pattern="^(?!.*(?:https?://|www\.))[a-zA-Z0-9\s,'-]*$"
                                                    title="Please enter a valid library name without URLs" />
                                            </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <input type="text" id="google_map_url" placeholder="Google Map URL*"
                                                    class="form-control form-control-lg" name="google_map_url" required
                                                    title="Please enter a valid Own Library Google Map URLs" />
                                            </div>
                                            <small id="url_error" class="text-danger"></small>
                                            </div>
                                        </div>


                                        {% endif %}

                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="email" id="form3Example8" placeholder="Email*"
                                                        class="form-control form-control-lg" name="email" required />
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div data-mdb-input-init class="form-outline">
                                                    <input type="number" id="form3Example8" placeholder="Phone Number*"
                                                        class="form-control form-control-lg" name="phone" required />
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            <div data-mdb-input-init class="form-outline mb-2">
                                                <input type="text" id="form3Example8" placeholder="Address*"
                                                    class="form-control form-control-lg" name="address" required />
                                            </div>
                                        </div>
                                        </div>

                                        <div>
                                            <div class="input-group mb-1">
                                                <input type="password" id="form3Example9" name="password1"
                                                    placeholder="Password*" class="form-control form-control-lg">
                                                <button class="btn btn-outline-secondary" type="button"
                                                    id="togglePassword">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <small id="passwordHelp" class="form-text"></small>

                                            <div class="input-group mb-1">
                                                <input type="password" id="form3Example91" name="password2"
                                                    placeholder="Confirm Password*"
                                                    class="form-control form-control-lg">
                                                <button class="btn btn-outline-secondary" type="button"
                                                    id="togglePassword1">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <small id="confirmPasswordHelp" class="form-text"></small>
                                        </div>

                                        <div class="align-items-center mb-3 mt-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value=""
                                                    id="rememberMe">
                                                <label class="form-check-label" for="rememberMe">
                                                    Remember me
                                                </label>
                                            </div>

                                        </div>

                                        <hr>
                                        <div class="pt-1">
                                            <div class="my-1 justify-content-between">
                                              <!-- <div class=" d-flex justify-content-start" id="button">
                                                <a href="{% url 'google_login' %}" class="gbtn btn btn-light btn-block d-flex align-items-center">
                                                  <img src="/static/img/figma_files/google_g.png" alt="Google logo" style="margin-right: 10px;">
                                                  Sign in with Google
                                                </a>
                                              </div> -->
                                              <div class="my-1">
                                                <button data-mdb-button-init data-mdb-ripple-init
                                                class="btn btn-block w-100 text-white" style="background-color: #294282;" type="submit" id="registerButton"
                                                disabled>Register</button>
                                              </div>
                                            </div>

                                        <!-- <div class="text-center ">
                                                <a href="{% url 'google_login' %}" class="google-btn" aria-label="Sign in with Google">
                                                    <img src="/static/img/figma_files/google_g.png" alt="Google logo" class="google-logo">
                                                    <span>Sign in with Google</span>
                                                </a>
                                        </div> -->

                                        {% if role == "librarian" %}
                                        <p class="mb-0 pb-lg-2 mt-3" style="color: #393f81;">
                                            Already have an account?
                                            <a href="/{{ role }}/login/" style="color: #393f81;">Login here</a>
                                        </p>
                                        <ul class="list-unstyled">
                                            <!-- <span class="label">Or Sign in with </span> <br>
                                            <a id="customBtn" href="{% url 'google_login' %}">
                                              <span class="icon"></span>
                                              <span class="buttonText">Sign in with Google</span>
                                            </a>
                                          </div> -->
                                            </li>
                                        </ul>
                                        {% elif role == "sublibrarian" %}
                                        <a href="/librarian/dashboard/" class="small text-muted"
                                            style="font-size: 18px;">Back to Dashboard</a><br>
                                        {% endif %}
                                        <div class="text-center">
                                            <a href="#!" class="small text-muted" style="font-size: 18px;">Terms of use.</a>
                                            <a href="#!" class="small text-muted" style="font-size: 18px;">Privacy
                                                policy</a>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </section>
    <script>
        // Wrap all code in an IIFE to avoid polluting the global scope
        (function() {
            'use strict';

            // Function to allow only alphabets
            function allowOnlyAlphabets(input) {
                input.value = input.value.replace(/[^A-Za-z ]/g, '');
            }

            // Function to validate phone number
            function validatePhone(input) {
                input.value = input.value.replace(/\D/g, '').slice(0, 10);
            }

            // Function to validate library name
            function validateLibraryName(input) {
                // Remove any URL-like patterns
                input.value = input.value.replace(/(?:https?:\/\/|www\.)\S+/gi, '');
                // Remove any special characters except comma, apostrophe, and hyphen
                input.value = input.value.replace(/[^a-zA-Z0-9\s,'-]/g, '');
            }

            function validateMapUrl(){
                const urlInput = document.getElementById('google_map_url');
                const urlError = document.getElementById('url_error');
                let valid = true;
                if (urlInput.value.length > 225) {
                        // urlError.style.display = 'block';
                        urlError.textContent = "URL cannot be greater than 225 characters.";
                        valid = false;
                    }else{
                        urlError.textContent = "";
                        valid = true;
                    }
            }

            // Function to validate password
            function validatePassword() {
                const password = document.getElementById('form3Example9').value.trim();
                const passwordHelp = document.getElementById('passwordHelp');
                let valid = true;

                if (!/[A-Z]/.test(password)) {
                    passwordHelp.textContent = "Password must contain at least one uppercase letter.";
                    valid = false;
                } else if (!/\d/.test(password)) {
                    passwordHelp.textContent = "Password must contain at least one number.";
                    valid = false;
                } else if (!/[!@#$%^&*]/.test(password)) {
                    passwordHelp.textContent = "Password must contain at least one special character (!@#$%^&*).";
                    valid = false;
                } else if (password.length < 8) {
                    passwordHelp.textContent = "Password must be at least 8 characters long.";
                    valid = false;
                } else {
                    passwordHelp.textContent = "Password is valid.";
                    valid = true;
                }

                passwordHelp.classList.toggle('valid', valid);
                passwordHelp.classList.toggle('invalid', !valid);

                validateConfirmPassword();
                checkFormValidity();
            }

            // Function to validate confirm password
            function validateConfirmPassword() {
                const password = document.getElementById('form3Example9').value.trim();
                const confirmPassword = document.getElementById('form3Example91').value.trim();
                const confirmPasswordHelp = document.getElementById('confirmPasswordHelp');

                const isValid = password === confirmPassword && confirmPassword.length > 0;
                confirmPasswordHelp.textContent = isValid ? "Passwords match." : "Passwords do not match.";
                confirmPasswordHelp.classList.toggle('valid', isValid);
                confirmPasswordHelp.classList.toggle('invalid', !isValid);

                checkFormValidity();
            }

            // Function to check form validity
            function checkFormValidity() {
                const passwordHelp = document.getElementById('passwordHelp').classList.contains('valid');
                const confirmPasswordHelp = document.getElementById('confirmPasswordHelp').classList.contains('valid');
                const registerButton = document.getElementById('registerButton');

                registerButton.disabled = !(passwordHelp && confirmPasswordHelp);
            }

            // Function to toggle password visibility
            function togglePasswordVisibility(inputId, buttonId) {
                const passwordField = document.getElementById(inputId);
                const toggleButton = document.getElementById(buttonId);
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                toggleButton.querySelector('i').classList.toggle('fa-eye-slash');
            }

            // Function to check password match on form submission
            function checkPasswordMatch() {
                const password = document.getElementById('form3Example9').value.trim();
                const confirmPassword = document.getElementById('form3Example91').value.trim();
                if (password === confirmPassword) {
                    return true; // Allow form submission
                } else {
                    alert("Passwords do not match.");
                    return false; // Prevent form submission
                }
            }

            // Function to hide alerts after 10 seconds
            function setupAlertDismissal() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function (alert) {
                    setTimeout(function () {
                        alert.style.transition = 'opacity 1s';
                        alert.style.opacity = '0';
                        setTimeout(function () {
                            alert.style.display = 'none';
                        }, 1000);
                    }, 10000);
                });
            }

            // Event listeners setup
            function setupEventListeners() {
                const firstNameInput = document.getElementById('form3Example1m');
                if (firstNameInput) {
                    firstNameInput.addEventListener('input', function () {
                        allowOnlyAlphabets(this);
                    });
                }

                const lastNameInput = document.getElementById('form3Example1n');
                if (lastNameInput) {
                    lastNameInput.addEventListener('input', function () {
                        allowOnlyAlphabets(this);
                    });
                }

                const phoneInput = document.querySelector('input[name="phone"]');
                if (phoneInput) {
                    phoneInput.addEventListener('input', function () {
                        validatePhone(this);
                    });
                }

                const libraryNameInput = document.getElementById('libraryName');
                if (libraryNameInput) {
                    libraryNameInput.addEventListener('input', function () {
                        validateLibraryName(this);
                    });
                }

                const mapUrlInput = document.getElementById('google_map_url');
                if (mapUrlInput) {
                    mapUrlInput.addEventListener('input', validateMapUrl);
                }

                const passwordInput = document.getElementById('form3Example9');
                if (passwordInput) {
                    passwordInput.addEventListener('input', validatePassword);
                }

                const confirmPasswordInput = document.getElementById('form3Example91');
                if (confirmPasswordInput) {
                    confirmPasswordInput.addEventListener('input', validateConfirmPassword);
                }

                const togglePasswordButton = document.getElementById('togglePassword');
                if (togglePasswordButton) {
                    togglePasswordButton.addEventListener('click', function() {
                        togglePasswordVisibility('form3Example9', 'togglePassword');
                    });
                }

                const togglePassword1Button = document.getElementById('togglePassword1');
                if (togglePassword1Button) {
                    togglePassword1Button.addEventListener('click', function() {
                        togglePasswordVisibility('form3Example91', 'togglePassword1');
                    });
                }

                const form = document.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function(event) {
                        if (!checkPasswordMatch()) {
                            event.preventDefault();
                        }
                    });
                }
            }

            // Initialize everything when the DOM is fully loaded
            document.addEventListener('DOMContentLoaded', function () {
                setupEventListeners();
                setupAlertDismissal();
            });

            // Google Analytics
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('js', new Date());
            gtag('config', 'G-J6148FSF9M');
        })();
    </script>


    <script>
        window.addEventListener("load", function () {
            const path = window.location.pathname; // Get current page path
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

            // Increment count for the current path
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

            // Store updated data back to localStorage
            localStorage.setItem("page_data", JSON.stringify(pageData));
          });

          // Function to send page view data
          function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

            if (Object.keys(pageData).length > 0) {
              fetch(location.origin + "/librarian/track-page-view/", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-CSRFToken": "{{ csrf_token }}",
                },
                body: JSON.stringify(pageData),
              })

                .then(() => {
                  localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
                localStorage.removeItem("page_data");
            } else {

              console.log("No page data to send");
            }
          }

          // Send data every 10 seconds
          setInterval(sendPageData, 10000);
    </script>
</body>

</html>