<!DOCTYPE html>
<html lang="en">
<head>
    {% load bootstrap5 %}
    {% bootstrap_css %}
    {% bootstrap_javascript %}
    <meta charset="utf-8">
    <meta name="google" content="notranslate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Verify OTP</title>
    <meta name="description" content="OTP Verification Page">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

        
    <style>
              body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>
</head>
<body>

    <section class="vh-100" style="background-color: #eee;">
        <div class="container h-100">
            <div class="row d-flex justify-content-center align-items-center h-100">
                <div class="col-lg-12 col-xl-11">
                    <div class="card text-black" style="border-radius: 25px;">
                        <div class="card-body p-md-5">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-6 col-xl-5 order-2 order-lg-1">
                                    <p class="text-center h1 fw-bold mb-5 mx-1 mx-md-4 mt-4">Verify OTP</p>
                                    
                                    {% if messages %}
                                        <div id="messages">
                                            {% for message in messages %}
                                                <div class="alert alert-success" role="alert">
                                                    {{ message }}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <form method="post">
                                        {% csrf_token %}
                                        <input type="hidden" name="email" value="{{ email }}" />

                                        <div class="mb-4">
                                            <label for="otp" class="form-label">OTP:</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                                <input type="text" id="otp" name="otp" class="form-control" required />
                                            </div>
                                        </div>

                                        <div class="mb-4">
                                            <label for="new_password" class="form-label">New Password:</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                <input type="password" id="new_password" name="new_password" class="form-control" required />
                                            </div>
                                        </div>

                                        <div class="mb-4">
                                            <label for="confirm_password" class="form-label">Confirm Password:</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required />
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-center">
                                            <button type="submit" class="btn btn-primary btn-lg">Set Password</button>
                                        </div>
                                    </form>
                                </div>
                                
                                <div class="col-md-10 col-lg-6 col-xl-7 d-flex align-items-center order-1 order-lg-2">
                                    <img src="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-registration/draw1.webp" class="img-fluid" alt="Sample image" loading="lazy">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        setTimeout(function() {
            var messages = document.getElementById('messages');
            if (messages) {
                messages.style.display = 'none';
            }
        }, 5000); // 5 seconds
    </script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
</body>
</html>
