const CACHE_NAME = 'librainian-v2.44';
const urlsToCache = [
  '/',
  '/static/css/style.css',
  '/static/js/main.js',
  '/static/img/librainian-logo-black-transparent-med.png',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => response || fetch(event.request))
  );
});

// ... existing service worker code ...

self.addEventListener('push', function(event) {
    const options = {
      body: event.data.text(),
      icon: '/static/img/librainian-logo-black-transparent.png',
      badge: '/static/img/librainian-logo-black-transparent.png'
    };
  
    event.waitUntil(
      self.registration.showNotification('Librainian Notification', options)
    );
  });
  
  self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    event.waitUntil(
      clients.openWindow('https://librainian.com')
    );
  });