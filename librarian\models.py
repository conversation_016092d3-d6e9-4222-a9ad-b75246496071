from django.utils import timezone
from django.db import models
from django.contrib.auth.models import User
from manager.models import *
from django.utils.text import slugify
from django.urls import reverse


class Librarian_param(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    manager = models.ForeignKey(Manager_param, on_delete=models.CASCADE)
    library_name = models.CharField(max_length=100)
    librarian_phone_num = models.BigIntegerField(default=9999999999)
    librarian_role = models.CharField(max_length=50, default="Librarian")
    librarian_address = models.TextField()
    discount_available = models.BooleanField(default=False)
    discount_amount = models.IntegerField(default=0)
    description = models.TextField(null=True, blank=True)
    is_librarian = models.BooleanField(default=False)
    image = models.ImageField(upload_to="librarian_images/", null=True, blank=True)
    google_map_url = models.URLField(max_length=510, blank=True, null=True)
    slug = models.SlugField(max_length=250, blank=True)

    def __str__(self):
        return f"{self.library_name} | {self.user.first_name} {self.user.last_name} | {self.librarian_role}"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.user.username)

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse("library-details", args=[str(self.slug)])


class QRCode(models.Model):
    librarian = models.OneToOneField(Librarian_param, on_delete=models.CASCADE)
    qr_code = models.TextField()  # Store the QR code as a base64 string

    def __str__(self):
        return f"{self.librarian.library_name} | QR Code"


class DailyTransaction(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    opening_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    closing_balance_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    closing_balance_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    sales_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_online = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    deposit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    transaction_count = models.IntegerField(default=0)
    total_student_fees_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_invoice_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_daily_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    def __str__(self):
        return f"Transaction Date : {self.date}"


def get_current_month():
    return timezone.now().strftime("%B")  # Returns the full month name, e.g., 'July'


def get_current_year():
    return timezone.now().year  # Returns the current year, e.g., 2024


class MonthlyTransaction(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    month = models.CharField(max_length=10, default=get_current_month)
    year = models.IntegerField(default=get_current_year)
    top_month = models.CharField(max_length=10, blank=True, null=True)
    low_month = models.CharField(max_length=10, blank=True, null=True)
    expensive_month = models.CharField(max_length=10, blank=True, null=True)
    affordable_month = models.CharField(max_length=10, blank=True, null=True)
    most_student_registration_month = models.CharField(
        max_length=10, blank=True, null=True
    )
    most_visitor_registration_month = models.CharField(
        max_length=10, blank=True, null=True
    )

    sales_cash_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_income_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_income_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    deposit_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    sales_return_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    transaction_count_monthly = models.IntegerField(default=0)
    total_student_fees_collection_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_invoice_collection_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_monthly_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    def __str__(self):
        return f"Monthly Transaction : {self.month} {self.year}"


class ContactMessage(models.Model):
    library = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    email = models.EmailField()
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    slug = models.SlugField(unique=True, max_length=255, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            if not self.created_at:
                self.created_at = timezone.now()
            base_slug = slugify(
                f"{self.name}-{self.created_at.strftime('%Y%m%d%H%M%S')}"
            )
            slug = base_slug
            counter = 1
            while ContactMessage.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Message from {self.name} to {self.library.user.first_name}"


class ComplaintTicket(models.Model):
    ticket_number = models.CharField(
        max_length=20, unique=True, blank=True, editable=False
    )
    issue_type = models.CharField(max_length=20)
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    subject = models.CharField(max_length=255)
    description = models.TextField()
    status = models.CharField(max_length=20, default="Open")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            self.ticket_number = self.generate_unique_ticket_number()
        super().save(*args, **kwargs)

    def generate_unique_ticket_number(self):
        """
        Generate a unique incrementing ticket number.
        """
        last_ticket = ComplaintTicket.objects.order_by("id").last()
        if last_ticket:
            last_number = int(last_ticket.ticket_number.split("-")[-1])
            new_number = last_number + 1
        else:
            new_number = 1
        return f"TICKET-{new_number:04d}"

    def __str__(self):
        return f"Ticket {self.ticket_number} - {self.subject}"


class DeviceToken(models.Model):
    """
    Model to store Firebase Cloud Messaging device tokens
    """

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="device_tokens"
    )
    token = models.CharField(max_length=500, unique=True)
    device_type = models.CharField(
        max_length=20, choices=[("android", "Android"), ("ios", "iOS"), ("web", "Web")]
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}'s {self.device_type} token"

    class Meta:
        unique_together = ("user", "token")
