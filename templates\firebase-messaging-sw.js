importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-messaging.js");

// Initialize Firebase with the configuration from your application
firebase.initializeApp({
  apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
  authDomain: "librainian-app.firebaseapp.com",
  projectId: "librainian-app",
  storageBucket: "librainian-app.firebasestorage.app",
  messagingSenderId: "623132670328",
  appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
  measurementId: "G-XNDKJL6JWH"
});

const messaging = firebase.messaging();

// Handle background messages
messaging.setBackgroundMessageHandler(function (payload) {
  console.log("Received background message ", payload);

  const notificationTitle = payload.notification.title || "Librainian Notification";
  const notificationOptions = {
    body: payload.notification.body || "New notification from Librainian",
    icon: payload.notification.icon || "/static/img/librainian-logo-black-transparent.png",
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Service worker lifecycle events
self.addEventListener('install', (event) => {
  console.log('Firebase Messaging Service Worker installing.');
});

self.addEventListener('activate', (event) => {
  console.log('Firebase Messaging Service Worker activating.');
});

// Handle notification clicks
self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients.matchAll({
      type: "window"
    })
    .then(function(clientList) {
      for (var i = 0; i < clientList.length; i++) {
        var client = clientList[i];
        if (client.url === '/' && 'focus' in client)
          return client.focus();
      }
      if (clients.openWindow)
        return clients.openWindow('/');
    })
  );
});
