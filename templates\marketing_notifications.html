<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketing Campaign</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

             <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }
        /* Custom Styles */
        .seat {
            width: 50px;
            height: 50px;
            margin: 5px;
            background-color: #ddd;
            border-radius: 5px;
            display: inline-block;
            text-align: center;
            line-height: 50px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .seat.selected {
            background-color: #28a745;
            color: white;
        }
        .seat.unavailable {
            background-color: #ff6666;
            cursor: not-allowed;
        }
        .seat:hover:not(.selected):not(.unavailable) {
            background-color: #007bff;
            color: white;
        }
        .seat-map {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        .row {
            display: flex;
            justify-content: center;
        }
        .front {
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }
        

        

        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar h4 {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #495057;
        }

        .sidebar a {
            color: #fff;
            padding: 15px 10px;
            padding-left: 25px;
            display: block;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .sidebar a:hover,
        .sidebar a.active {
            background-color: #495057;
            border-left: 3px solid #007bff;

        }

        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        /* .form-control {
            border: none;
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        } */

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }

        /* .search-wrapper {
      position: relative;
    }

    .search-wrapper input[type="search"] {
      padding-left: 2.5rem;
    }

    .search-wrapper .fa-magnifying-glass {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #0b0b0b;
    }

    .card {
      margin-bottom: 20px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .card .card-body {
      position: relative;
      padding: 20px;
    }

    .card .card-body .icon {
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.15);
    }

    .card .card-footer {
      background-color: transparent;
      border-top: none;
    } */

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                display: none;
                /* Adjusted to include display: none */
            }

            .navbar {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .navbar-toggler {
                display: none;
            }

            .navbar-light .navbar-brand {
                padding-left: 80px;
            }

        }

        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: -8px;
            left: -350px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            /* padding-top: 40px; */
            padding-bottom: 90px;
            z-index: 1051;
            white-space: nowrap;
            /* width: 374px; */
            width: 150vw;
            height: 100vh;
            text-align: center;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 5px;
            font-size: 20px;
            padding-left: 60px;
            margin-bottom: 10px;
        }
        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }
        

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }
        
        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-success-2 {
            background-color: #26a845;
            color: #fff;
        }
        .btn-success-2:hover {
            background-color: #219a3d;
            color: #fff;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }
        
        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }
        
        .btn-danger-2 {
            background-color: #df3443;
            color: #fff;
        }
        .btn-danger-2:hover {
            background-color: #aa2631;
            color: #fff;
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }
        .container {
            flex: 1;
        }

        .card {
            border: none;
            border-radius: 1rem;
            background-color: #fff;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            margin-bottom: 20px; /* Added margin-bottom to avoid overlap with the footer */
        }

        .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            color: #f8f9fa;
            background: rgb(0,185,215);
            background: linear-gradient(283deg, rgba(0,185,215,0.9416141456582633) 0%, rgba(0,49,153,0.8883928571428571) 100%);
            text-align: center;
            padding: 20px;
        }

        .card-header h2 {
            font-size: 1.5rem;
        }

        .form-group label {
            font-weight: bold;
        }

        .form-group input[readonly] {
            background-color: #e9ecef;
            opacity: 1;
        }

        .btn-dark {
            background-color: #343a40;
            border-color: #343a40;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .btn-dark:hover {
            background-color: #23272b;
            border-color: #1d2124;
        }

        .form-row {
            margin-bottom: 15px;
        }

        /* Footer styling */
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /* 
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        .seatSel{
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        .eachSeat{
            padding: 10px;
            border: 2px solid #28a746;
            color: #28a746;
            border-radius: 5px;
            margin: 10px;
            font-weight: 600;
        }
        #seatForm{
            width: fit-content;
            margin: 0 auto;
            /* margin-top: 10px; */
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.3/tinymce.min.js"></script>
</head>
<body class="">
    <!-- Sidebar -->
    {% include "sidebar.html" %}

    <div class="m-3">
        <div class="card">
            <div class="card-header">
                <h1 class="font-weight-bold">Marketing Campaign</h1>
                <p class="text-center font-weight-bold">Create and send notifications to selected students</p>
            </div>
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-primary alert-dismissible fade show" role="alert">
                        {{ message }}<br>
                    </div>
                    {% endfor %}
                {% endif %}
                <form id="marketingForm" method="POST" action="/librarian/send-notifications/">
                    {% csrf_token %}
                    <div class="mb-4">
                        <h3><u>Selected Students</u></h3>
                        <div class="d-flex flex-wrap">
                            {% for student in selected_students %}
                            <div class="card m-2 p-2 shadow border" style="width: 18rem;">
                                <input type="hidden" name="student_ids[]" value="{{ student.id }}">
                                <p class="font-weight-bold text-center">{{ student.name }}</p>
                                <p class="text-muted text-center">{{ student.email }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="form-row">
                        <!-- Email Section -->
                        <div class="col-md-6 mb-4">
                            <h3><u>Email Template</u></h3>
                            <br>
                            <label class="font-weight-bold">Select Template:</label>
                            <select name="email_template" class="form-control mb-3" id="emailTemplateSelect">
                                <option value="">Choose a template...</option>
                                <option value="welcome">Welcome Email</option>
                                <option value="reminder">Payment Reminder</option>
                                <option value="announcement">Announcement</option>
                                <option value="custom">Custom Template</option>
                            </select>
                            
                            <label class="font-weight-bold">Email Subject:</label>
                            <input type="text" name="email_subject" class="form-control mb-3">

                            <label class="font-weight-bold">Email Content:</label>
                            <textarea id="emailContent" name="email_content" class="form-control"></textarea>
                            <button type="submit" class="btn btn-primary float-right">Send Notifications via Mail</button>

                        </div>
                    </form>
                    <form id="marketingForm" method="POST" action="/librarian/send-notifications/"> 
                        <!-- SMS Section -->
                        <div class="col-md-6 mb-4">
                            <h3><u>SMS Template</u></h3>
                            <br>
                            <label class="font-weight-bold">Select Template:</label>
                            {% csrf_token %}
                            {% for student in selected_students %}
                                <input type="hidden" name="student_ids[]" value="{{ student.id }}">
                            {% endfor %}
                            <select name="sms_template" class="form-control mb-3" id="smsTemplateSelect">
                                <option value="">Titles...</option>
                                {% for template_content  in template_contents %}
                                <option value="{{template_content}}">{{template_content}}</option>
                                {% endfor %}
                            </select>

                            <label class="font-weight-bold">SMS Content:</label>
                            <textarea name="sms_content" class="form-control" rows="4" maxlength="160" disabled="true"></textarea>
                            <small class="form-text text-muted">Maximum 160 characters</small>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary float-right">Send Notifications via SMS</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">        <!-- <p>Developed with passion by Librainian</p> -->
    </div>

    <script>
        // Initialize TinyMCE
        tinymce.init({
            selector: '#emailContent',
            plugins: 'advlist autolink lists link image charmap preview anchor',
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | link image',
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Handle email template selection
            document.getElementById('emailTemplateSelect').addEventListener('change', function(e) {
                if (e.target.value !== 'custom') {
                    loadEmailTemplate(e.target.value);
                }
            });

            // Handle SMS template selection
            document.getElementById('smsTemplateSelect').addEventListener('change', function(e) {
                const selectedOption = e.target.options[e.target.selectedIndex]; 
                var templateContentDict = {{ template_contents|safe }};
                document.querySelector('[name="sms_content"]').value = templateContentDict[selectedOption.value].content;
            });
        });

        function loadEmailTemplate(template) {
            const templates = {
                'welcome': {
                    subject: 'Welcome to Our Library',
                    content: `<p>Dear {student_name},</p><p>Welcome to our library!</p><p>Best regards,<br>Library Team</p>`
                },
                'reminder': {
                    subject: 'Payment Reminder',
                    content: `<p>Dear {student_name},</p><p>Reminder about your pending library fees.</p><p>Best regards,<br>Library Team</p>`
                },
                'announcement': {
                    subject: 'Important Announcement',
                    content: `<p>Dear {student_name},</p><p>We have an important announcement.</p><p>Best regards,<br>Library Team</p>`
                }
            };

            const selectedTemplate = templates[template];
            if (selectedTemplate) {
                document.querySelector('[name="email_subject"]').value = selectedTemplate.subject;
                tinymce.get('emailContent').setContent(selectedTemplate.content);
            }
        }

        
    </script>

    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">


</body>
</html>
