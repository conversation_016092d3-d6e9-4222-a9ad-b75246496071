from django.urls import path
from . import views


urlpatterns = [
    path("signup/", views.sublibrarian_signup, name="sub_signup"),
    path("login/", views.sublibrarian_login, name="sub_login"),
    path("logout/", views.sublibrarian_logout, name="logout"),
    path("profile/", views.sublibrarian_profile, name="profile"),
    path("table/", views.library_data, name="student-data"),
    path("edit-profile/", views.edit_sublibrarian_profile, name="edit_profile"),
    path("help/", views.help_page, name="help_page"),
    path("dashboard/", views.dashboard, name="dashboard"),
    path("feedback/", views.feedback_page, name="feedback_page"),
    path("daily-transaction/", views.daily_transactions, name="daily-transaction-sublib"),
    path("shifts/", views.shifts_create, name="shifts_create_sub"),
    path("shifts/update/<int:pk>/", views.shifts_update, name="shifts_update_sub"),
    path("shifts/delete/<int:pk>/", views.shifts_delete, name="shifts_delete_sub"),
    path("seats/", views.create_seat, name="seats-list_sub"),
    path("update-seat/<int:pk>", views.update_seat, name="update_seat_sub"),
]
