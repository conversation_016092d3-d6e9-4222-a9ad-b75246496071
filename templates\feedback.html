<!DOCTYPE html>
<html lang="en" translate="no">

<head>
<meta charset="UTF-8">
<meta name="google" content="notranslate">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="robots" content="index, follow">
<meta name="keywords" content="library feedback, user feedback, library app, productivity, improve library services">
<meta name="description" content="We value your feedback! Help us improve our library app by sharing your thoughts. Your insights are crucial for enhancing user experience.">
<meta property="og:title" content="Feedback for Our Library App">
<meta property="og:description" content="Your feedback is important to us! Share your thoughts and help us improve our library app.">
<meta property="og:url" content="https://www.librainian.com/feedback">
<meta property="og:image" content="https://img.freepik.com/free-vector/organic-flat-feedback-concept_52683-62653.jpg">
<meta itemprop="author" content="Librainian Team">
<meta itemprop="datePublished" content="2024-01-01">
<meta itemprop="dateModified" content="2024-01-01">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Feedback for Librainian App">
<meta name="twitter:description" content="Help us improve our library app by providing your feedback!">
<meta name="twitter:image" content="https://img.freepik.com/free-vector/organic-flat-feedback-concept_52683-62653.jpg">

    <title>Feedback</title>
     
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          

    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        .show {
            display: block;
        }


        .form-control:focus {
            box-shadow: none;
        }


       
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        }

    @media screen and (max-width: 768px) {
        .feedback_p {
            font-size: 1rem;
            margin: 0;
            padding: 0; /* Adjust to a smaller font size */
        }
        .feedback_h2 {
            font-size: 1.3rem;
        }
        .emoji{
            margin: 0;
            padding: 0; /* Adjust to a smaller font size */        }
    }

    /* Tablet view (481px to 768px width) */
    @media screen and (min-width: 767px ) and (max-width: 1025px) {
        .feedback_p {
            font-size: 0.9rem; /* Adjust to a medium font size */
        }
        .emoji{
            font-size: 20px;
        }
        .feedback_h2 {
            font-size: 1.2rem;
        }
    }
    </style>
</head>

<body>

    <!-- Sidebar -->  
       {% include "sidebar.html" %}

        <div class="container">
            <h2 class="mt-3 feedback_h2 text-center">We Value Your Feedback</h2>
                {% if messages %}
                    <div id="messageContainer">
                        {% for message in messages %}
                        {% if message.tags == 'success' %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {% elif message.tags == 'error' %}
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                {% elif message.tags == 'warning' %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    {% elif message.tags == 'info' %}
                                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                                        {% else %}
                                        <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                            {% endif %}
                                            {{ message }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
            <div class="feedback-container mt-4 p-3 text-center" style="background: #cee3e0 !important; border-radius: 1rem;">
                <p class="feedback_p" style="color: #28345a;">We would like your feedback to improve our website.</p>
                <p class="feedback_p" style="color: #325c6a;">What is your opinion of this page?</p>
                
                <div class="mb-3">
                    <span class="emoji" onclick="addEmoji('😊')">😊</span>
                    <span class="emoji" onclick="addEmoji('😢')">😢</span>
                    <span class="emoji" onclick="addEmoji('👍')">👍</span>
                    <span class="emoji" onclick="addEmoji('❤️')">❤️</span>
                    <span class="emoji" onclick="addEmoji('😡')">😡</span>
                    <span class="emoji" onclick="addEmoji('😎')">😎</span>
                </div>
        
                <form method="POST">
                    {% csrf_token %}
                    <div class="form-group">
                        <input type="text" class="form-control" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" class="form-control" id="email"  name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <textarea class="form-control" id="feedback" rows="4"  name="feedback" placeholder="Your Feedback" required></textarea>
                    </div>
                    <button type="submit" class="btn" style="background: #294282 !important; color: #fff;">Submit Feedback</button>
                </form>
            </div>
        </div>

        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
            <!-- <p>Developed with passion by Librainian</p> -->
        </div>

    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>






    <script>
        // Show success message and hide after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function (alert) {
                setTimeout(function () {
                    alert.style.transition = 'opacity 1s';
                    alert.style.opacity = '0';
                    setTimeout(function () {
                        alert.style.display = 'none';
                    }, 1000);
                }, 10000);
            });
        });
    </script>  
    <script>
        function addEmoji(emoji) {
            const feedbackTextarea = document.getElementById('feedback');
            feedbackTextarea.value += emoji;
            feedbackTextarea.focus(); // Bring focus back to the textarea
        }
    </script>
    <script>
        document.getElementById("userDropdown").addEventListener("click", function () {
            document.getElementById("userDropdownContent").classList.toggle("show");
        });

        // Close the dropdown if the user clicks outside of it
        window.addEventListener("click", function (event) {
            if (!event.target.matches("#userDropdown")) {
                var dropdowns = document.getElementsByClassName("dropdown-content");
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains("show")) {
                        openDropdown.classList.remove("show");
                    }
                }
            }
        });

    </script>
       
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>


    <!-- <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css"> -->
</body>

</html>