<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Device Token Registration</title>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging.js"></script>

    <script>
    // Your web app's Firebase configuration
    const firebaseConfig = {
        apiKey: "AIzaSyBZZvIp2gXXdQk5kpLnIQGkdgQgP7Vz4aw",
        authDomain: "librainian-d44e1.firebaseapp.com",
        projectId: "librainian-d44e1",
        storageBucket: "librainian-d44e1.firebasestorage.app",
        messagingSenderId: "862158269429",
        appId: "1:862158269429:web:6a0ac81f4136188411cebc",
        measurementId: "G-3L2X8JMGF2"
    };

    // Initialize Firebase
    const app = firebase.initializeApp(firebaseConfig);
    const messaging = firebase.messaging(app);

    // Request permission to send notifications
    messaging.requestPermission()
        .then(() => {
            console.log("Notification permission granted.");
            return messaging.getToken();
        })
        .then((token) => {
            console.log("FCM Token: ", token);
            // Send the token to your Django backend for storage
            sendTokenToServer(token);
        })
        .catch((err) => {
            console.error("Permission denied or error getting token", err);
            alert("Error: Notification permission denied or token fetch failed.");
        });

    // Function to send the token to the server (Django backend)
    function sendTokenToServer(token) {
        // Detect device type
        let deviceType = 'web';  // Default to 'web'
        if (navigator.userAgent.match(/Android/i)) {
            deviceType = 'android';
        } else if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
            deviceType = 'ios';
        }

        fetch('/librarian/save-device-token/', {  // or '/librarian/save-device-token/' if it's under the app prefix
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'), // CSRF token for Django
            },
            body: JSON.stringify({
                token: token,
                device_type: deviceType, // Pass the device type to the server
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                console.log(data.message);
            } else {
                console.log("Token saved successfully:", data);
            }
        })
        .catch(error => {
            console.error("Error saving token:", error);
            alert("Failed to save the device token.");
        });
    }

    // Function to get CSRF token from cookies (for CSRF protection in Django)
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    </script>
</head>
<body>
    <!-- Your page content here -->
    <h1>Device Token Registration</h1>
    <p>This page will send your device's token to the server for storage.</p>
</body>
</html>
