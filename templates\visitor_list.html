<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visitors</title>

    <!-- Fonts & Icons -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Bootstrap 5.0.0 & DataTables -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <!-- Custom Styles -->
    <style>
        body {
            background: #cee3e0 !important;
            font-family: 'Comfortaa', cursive;
        }

        .visitor_section_card {
            padding: 1rem;
            border-radius: 1rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: bold;
        }

        .badge {
            font-size: 0.875rem;
            margin-right: 0.25rem;
        }

        .footer {
            text-align: center;
            padding: 1rem;
            margin-top: 2rem;
        }

        .footer img {
            max-height: 50px;
            filter: invert(0.5) brightness(0);
            opacity: 0.6;
        }

        @media (max-width: 768px) {
            .table {
                display: none;
            }
        }

        .visitor-card {
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .visitor-card:hover {
            background-color: #f0f0f0;
        }
    </style>
</head>

<body>
    {% include "sidebar.html" %}

    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
            DASHBOARD / VISITORS
        </div>
    </div>

    <div class="container-fluid mt-3">

        <!-- Create New Visitor Button -->
        <div class="text-end mb-3">
            <a href="/visitors/create/" class="btn btn-primary">New Visitor</a>
        </div>

        <!-- Visitors Table (Desktop) -->
        <div class="row">
            <div class="col-12">
                <div class="card visitor_section_card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Visitors List</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="visitorsTable" class="table table-striped table-hover fs-6">
                                <thead>
                                    <tr>
                                        <th>S.no.</th>
                                        <th>Visitor ID</th>
                                        <th>Date</th>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Callback</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for visitor in visitors %}
                                    <tr>
                                        <td>{{ forloop.counter }}</td>
                                        <td><a href="/visitors/{{ visitor.slug }}">{{ visitor.inqid }}</a></td>
                                        <td>{{ visitor.date }}</td>
                                        <td>{{ visitor.name }}</td>
                                        <td>
                                            {{ visitor.contact }}
                                            <i class="bi bi-clipboard" style="cursor: pointer;" onclick="copyToClipboard('{{ visitor.contact }}')"></i>
                                            <a href="tel:{{ visitor.contact }}" class="ms-2">
                                                <i class="fas fa-phone-alt"></i>
                                            </a>
                                        </td>
                                        <td>{{ visitor.callback }}</td>
                                        <td>{{ visitor.status }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

        <!-- Card View (Mobile) -->
        <div class="row d-md-none">
            {% for visitor in visitors %}
            <div class="col-12 mb-3">
                <a href="/visitors/{{ visitor.slug }}/" class="visitor-card">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ visitor.name }}</h5>
                            <p><strong>Date:</strong> {{ visitor.date }}</p>
                            <p><strong>Contact:</strong> {{ visitor.contact }}</p>
                            <p><strong>Callback:</strong> {{ visitor.callback }}</p>
                            <p><strong>Status:</strong> <span class="badge bg-info">{{ visitor.status }}</span></p>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo">
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#visitorsTable').DataTable();
        });

        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        updateDateTime();
        setInterval(updateDateTime, 60000);

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function () {
                alert('Copied to clipboard');
            }, function () {
                alert('Failed to copy text');
            });
        }
    </script>
</body>

</html>
