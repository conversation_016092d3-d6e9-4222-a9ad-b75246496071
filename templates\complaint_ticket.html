<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complaint Management Dashboard</title>
     
    <meta name="google" content="notranslate">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Admin Dashboard">
    <link rel="apple-touch-icon" href="/static/img/librainian-logo-black-transparent.png">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="manifest" href="/static/js/manifest.json">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">



    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

    <script>
        document.addEventListener('keydown', function(event) {
            // For Windows/Linux: Disable Ctrl+Shift+I
            // if (event.ctrlKey && event.shiftKey && event.key === 'I') {
            //     event.preventDefault();
            //     alert('Inspect Element is disabled.');
            // }
    
            // For macOS: Disable Command+Option+I
            if (event.metaKey && event.altKey && event.key === 'I') {
                event.preventDefault();
                alert('Inspect Element is disabled.');
            }
    
            // Additionally block F12 key (common for all OS)
            if (event.key === 'F12') {
                event.preventDefault();
                alert('Inspect Element is disabled.');
            }
        });
    </script>
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        .container-fluid {
            max-width: 1280px;
           
        }

        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --background-color: #ecf0f1;
            --text-color: #34495e;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

     
        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        .form-control {
            /* border: none; */
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        }

        .form-control:focus {
            /* border: none; */
            outline: none;
            box-shadow: none;
        }

        .search-wrapper {
            position: relative;
        }

        .search-wrapper input[type="search"] {
            padding-left: 2.5rem;
        }

        .search-wrapper .fa-magnifying-glass {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #0b0b0b;
        }

        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card .card-body {
            position: relative;
            padding: 20px;
        }

        .card .card-body .icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.15);
        }

        .card .card-footer {
            background-color: transparent;
            border-top: none;
        }

  
           

        #notificationBtn {
            display: none;
        }

     

        .white_bg {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 10px;
        }

        h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
            font-size: 1.3rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: var(--primary-color);
            color: white;
        }

        tr:hover {
            background-color: #f5f5f5;
            cursor: pointer;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        label {
            font-weight: bold;
        }

        input {        
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            padding: 10px 20px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #2980b9;
        }

        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }

        .status-open {
            background-color: var(--warning-color);
            color: white;
        }

        .status-inprogress {
            background-color: var(--secondary-color);
            color: white;
        }

        .status-closed {
            background-color: var(--success-color);
            color: white;
        }

        #ticketDetails {
            display: none;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .detail-label {
            font-weight: bold;
        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }
    </style>
</head>

<body>
    

<!-- End Google Tag Manager (noscript) -->
    <!-- Sidebar -->
    {% include "sidebar.html" %}
    <section class="container-fluid m-4">
        <div class="row">
            <!-- Complaint Tickets -->
            <div class="col-md-5 white_bg p-5">
                <h2>Complaint Tickets</h2>
                <table id="complaintTable" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Ticket #</th>
                            <th>Subject</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ticket in tickets %}
                        <tr data-ticket-id="{{ ticket.id }}">
                            <td>{{ ticket.ticket_number }}</td>
                            <td>{{ ticket.subject }}</td>
                            <td><span class="status status-{{ ticket.status|lower }}">{{ ticket.status }}</span></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
    
            <!-- Ticket Details -->
            <div class="col-md-5 white_bg p-5">
                <h2>Ticket Details will appear here .</h2>
                <div id="ticketDetails">
                    <div class="detail-row">
                        <span class="detail-label">Ticket Number:</span>
                        <span id="detailId"></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Description:</span>
                        <span id="detailDescription"></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span id="detailStatus"></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Created At:</span>
                        <span id="detailCreatedAt"></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Updated At:</span>
                        <span id="detailUpdatedAt"></span>
                    </div>
                    <form id="statusForm" method="post">
                        {% csrf_token %}
                        <input type="hidden" id="ticket-id" name="ticket_id">
                        <label for="new-status">Change Status:</label>
                        <select id="new-status" name="status" class="form-select" required>
                            <option value="">Select status</option>
                            <option value="Open">Open</option>
                            <option value="In_Progress">In Progress</option>
                            <option value="Closed">Closed</option>
                        </select>
                        <button type="submit" class="btn btn-primary mt-3">Update Status</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
     


    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const ticketRows = document.querySelectorAll('#complaintTable tbody tr');
            const ticketDetails = document.getElementById('ticketDetails');
            const statusForm = document.getElementById('statusForm');

            ticketRows.forEach(row => {
                row.addEventListener('click', function () {
                    const ticketId = this.dataset.ticketId;
                    fetchTicketDetails(ticketId);
                });
            });

            statusForm.addEventListener('submit', function (e) {
                e.preventDefault();
                const ticketId = document.getElementById('ticket-id').value;
                const newStatus = document.getElementById('new-status').value;
                updateTicketStatus(ticketId, newStatus);
            });

            function fetchTicketDetails(ticketId) {
    fetch(`/librarycommander/get-ticket-details/${ticketId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('detailId').textContent = ticketId;
                document.getElementById('detailDescription').textContent = data.description;
                document.getElementById('detailStatus').textContent = data.status;
                document.getElementById('detailCreatedAt').textContent = data.created_at;
                document.getElementById('detailUpdatedAt').textContent = data.updated_at;

                document.getElementById('ticket-id').value = ticketId;
                ticketDetails.style.display = 'block';
            } else {
                console.error('Error fetching ticket details:', data.error);
                alert('Failed to fetch ticket details: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Request failed', error);
            alert('An error occurred while fetching ticket details.');
        });
}

            function updateTicketStatus(ticketId, newStatus) {
                fetch('/librarycommander/update-ticket-status/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        ticket_id: ticketId,
                        status: newStatus
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update the UI to reflect the new status
                            const row = document.querySelector(`tr[data-ticket-id="${ticketId}"]`);
                            const statusCell = row.cells[2].querySelector('span');
                            statusCell.textContent = newStatus;
                            statusCell.className = `status status-${newStatus.toLowerCase()}`;

                            document.getElementById('detailStatus').textContent = newStatus;

                            alert(`Status updated for Ticket #${ticketId} to ${newStatus}`);
                        } else {
                            alert('Error updating status');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            }
        });
    </script>
</body>

</html>