<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     
    <meta name="google" content="notranslate">
    <title>Create Invoice</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          

    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar h4 {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #495057;
        }

        .sidebar a {
            color: #fff;
            padding: 15px 10px;
            padding-left: 25px;
            display: block;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .sidebar a:hover,
        .sidebar a.active {
            background-color: #495057;
            border-left: 3px solid #007bff;

        }

        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        /* .form-control {
            border: none;
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        } */

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }

        /* .search-wrapper {
      position: relative;
    }

    .search-wrapper input[type="search"] {
      padding-left: 2.5rem;
    }

    .search-wrapper .fa-magnifying-glass {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #0b0b0b;
    }

    .card {
      margin-bottom: 20px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .card .card-body {
      position: relative;
      padding: 20px;
    }

    .card .card-body .icon {
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.15);
    }

    .card .card-footer {
      background-color: transparent;
      border-top: none;
    } */

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                display: none;
                /* Adjusted to include display: none */
            }

            .navbar {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .navbar-toggler {
                display: none;
            }

            .navbar-light .navbar-brand {
                padding-left: 80px;
            }

        }

        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }

        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }

        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }
        .container {
            flex: 1;
        }

        .card {
            border: none;
            border-radius: 1rem;
            background-color: #fff;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            margin-bottom: 20px; /* Added margin-bottom to avoid overlap with the footer */
        }

        .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            color: #f8f9fa;
            background: rgb(0,185,215);
            background: linear-gradient(283deg, rgba(0,185,215,0.9416141456582633) 0%, rgba(0,49,153,0.8883928571428571) 100%);
            text-align: center;
            padding: 20px;
        }

        .card-header h2 {
            font-size: 1.5rem;
        }

        .form-group label {
            font-weight: bold;
        }

        .form-group input[readonly] {
            background-color: #e9ecef;
            opacity: 1;
        }

        .btn-dark {
            background-color: #343a40;
            border-color: #343a40;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .btn-dark:hover {
            background-color: #23272b;
            border-color: #1d2124;
        }

        .form-row {
            margin-bottom: 15px;
        }

        /* Footer styling */
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /* 
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        #seatForm{
            width: fit-content;
            margin: 0 auto;
            /* margin-top: 10px; */
        }
    </style>
</head>

<body>
    
    <!-- Sidebar -->
    {% include "sidebar.html" %}

        <div class="m-3">
            <div class="card">
                <div class="card-header">
                    <h2>Create Invoice for {{ student.name }}</h2>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
    
                        <!-- Displaying student details as read-only text -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="student_name">Student Name:</label>
                                <input type="text" class="form-control" id="student_name" name="student_name"
                                    value="{{ student.name }}" readonly>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="course">Course:</label>
                                <input type="text" class="form-control" id="course" name="course"
                                    value="{{ student.course }}" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="age">Age:</label>
                                <input type="text" class="form-control" id="age" name="age" value="{{ student.age }}"
                                    readonly>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="gender">Gender:</label>
                                <input type="text" class="form-control" id="gender" name="gender"
                                    value="{{ student.gender }}" readonly>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="email">Email:</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ student.email }}"
                                    readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="mobile">Mobile:</label>
                                <input type="text" class="form-control" id="mobile" name="mobile"
                                    value="{{ student.mobile }}" readonly>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="locality">Locality:</label>
                                <input type="text" class="form-control" id="locality" name="locality"
                                    value="{{ student.locality }}" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="city">City:</label>
                                <input type="text" class="form-control" id="city" name="city" value="{{ student.city }}"
                                    readonly>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="state">State:</label>
                                <input type="text" class="form-control" id="state" name="state" value="{{ student.state }}"
                                    readonly>
                            </div>
                        </div>
    
                        <hr>
    
                        <!-- Invoice creation form -->
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="due_date">Due Date:</label>
                                <input type="date" class="form-control" id="due_date" name="due_date"
                                    value="{{ invoice.due_date }}">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="discount_amount">Discount Amount:</label>
                                <input type="number" class="form-control" id="discount_amount" name="discount_amount">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="shifts" required>Shifts:</label><br>
                                {% for shift in shifts %}
                                <input type="checkbox" id="shift{{ shift.id }}" name="shifts" value="{{ shift.id }}">
                                <label for="shift{{ shift.id }}">{{ shift.name }} | {{ shift.price }}</label><br>
                                {% endfor %}
                            </div>
                            {% comment %} <div class="form-group col-md-6">
                                <p>Seats</p>
                                <a href="">Seat Available</a>
                            </div> {% endcomment %}
                        </div> 

                        <div class="form-group">
                            <label for="months" required>Months:</label>
                            <div class="row">
                                {% for month in months %}
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="month{{ month.id }}"
                                            name="months" value="{{ month.id }}">
                                        <label class="form-check-label" for="month{{ month.id }}">{{ month.name }}</label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="mode_pay">Mode of Payment:</label>
                            <select class="form-control" id="mode_pay" name="mode_pay" required>
                                <option value="cash">Cash</option>
                                <option value="online">Online</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="description">Description:</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div id="seatForm">
                            <button type="submit" class="btn btn-dark btn-block">Book Your Seats</button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- Footer -->
            <div class="footer">
                <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
                <!-- <p>Developed with passion by Librainian</p> -->
            </div>
        </div>


    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


    


    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">
        <script>
            window.addEventListener("load", function () {
                const path = window.location.pathname; // Get current page path
                let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object
              
                // Increment count for the current path
                pageData[path] = pageData[path] ? pageData[path] + 1 : 1;
              
                // Store updated data back to localStorage
                localStorage.setItem("page_data", JSON.stringify(pageData));
              });
              
              // Function to send page view data
              function sendPageData() {
                const pageData = JSON.parse(localStorage.getItem("page_data")) || {};
              
                if (Object.keys(pageData).length > 0) {
                  fetch(location.origin + "/librarian/track-page-view/", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      "X-CSRFToken": "{{ csrf_token }}",
                    },
                    body: JSON.stringify(pageData),
                  })
                   
                    .then(() => {
                      localStorage.removeItem("page_data");
                    })
                    .catch((error) => console.error("Error sending page data:", error));
                    localStorage.removeItem("page_data");
                } else {
                    
                  console.log("No page data to send");
                }
              }
              
              // Send data every 10 seconds
              setInterval(sendPageData, 10000);
        </script>
    </body>

</html>