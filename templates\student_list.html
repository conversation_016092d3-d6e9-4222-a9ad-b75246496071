{% load django_bootstrap5 %}
<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page immediately
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    setTimeout(function() {
                        loader.style.display = 'none';
                    }, 100); // Small delay to ensure everything is rendered
                }
            });
        })();
    </script>

    {% bootstrap_css %}
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     
    <title>Students</title>
    <meta name="theme-color" content="#da3e01">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <!-- Global Loader Script -->
     


    <style>
        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        body h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        ul,
        li,
        strong,
        em,
        b,
        s,
        small,
        span {
            font-family: 'Comfortaa', sans-serif !important;
        }

        .card {
            border-radius: 1rem;
        }

        table {
            border-radius: 1rem;
        }

        .table {
            border-radius: 1rem !important;

        }

        .btn {
            border-radius: 1rem !important;
        }

        input {
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
            width: 90%;
        }

        select {
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
        }

        @media only screen and (max-width: 767px) {
            .d-sm-none {
                display: none !important;
            }
        }

        .card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 1rem;
        }



        .card-header {
            background: #294282 !important;
            /* background: linear-gradient(283deg, rgba(0, 185, 215, 0.9416141456582633) 0%, rgba(0, 49, 153, 0.8883928571428571) 100%); */
            color: #fff;
            text-align: center;
            border-top-left-radius: 1rem !important;
            border-top-right-radius: 1rem !important;
            border-radius: 1rem;
            padding: 20px;
            letter-spacing: 1px;
        }


        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }



        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }



        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */


        @media (max-width:766px) {
            .footer {
                margin-bottom: 50px;
                margin-top: 20px;
            }

            .footer img {
                width: 60%;
            }

            .table-responsive thead tr {
                display: grid;
                grid-template-columns: 50% 50%;
            }

            .card-text {
                font-size: 0.7rem;
            }

            .card-text strong {
                font-size: 1.1rem;
            }

        }

        #notificationBtn {
            display: none;
        }





        /* .card-header {
            background-color: #343a40;
            color: #fff;
        } */
        .card-title {
            margin: 0;
        }

        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }


        /* Table row colors */
        .bg-light-blue {
            background-color: #e6f3ff !important;
        }

        .bg-light-green {
            background-color: #e6ffe6 !important;
        }

        .bg-light-red {
            background-color: #ffe6e6 !important;
        }

        .bg-light-grey {
            background-color: #f2f2f2 !important;
        }

        /* Card styles */
        .student-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .student-card .card-header {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
        }

        .student-card .card-body {
            padding: 20px;
        }

        .student-card .card-title {
            font-weight: 600;
            color: #333;
        }

        .student-card .card-text {
            margin-bottom: 10px;
            color: #555;
        }

        .student-card .btn {
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Status color styles */
        .student-card.status-blue {
            background-color: #e6f2ff;
        }

        .student-card.status-blue .card-header {
            background-color: #cce5ff;
        }

        .student-card.status-green {
            background-color: #e6ffe6;
        }

        .student-card.status-green .card-header {
            background-color: #ccffcc;
        }

        .student-card.status-red {
            background-color: #ffe6e6;
        }

        .student-card.status-red .card-header {
            background-color: #ffcccc;
        }

        .student-card.status-grey {
            background-color: #f2f2f2;
        }

        .student-card.status-grey .card-header {
            background-color: #e6e6e6;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .student-cards .card {
                margin-bottom: 20px;
            }

            #table-body {
                display: none !important;
            }
        }

        @media only screen and (max-width: 767px) {

            #basic-datatables tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #294282;
            }

            #basic-datatables table {
                border-radius: 1rem;
            }

            #basic-datatables2 tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #e7fbfb;
            }

            #basic-datatables3 tr {
                padding: 1rem;
                border-radius: 1rem;
                background-color: #e7fbfb;
            }


            .small_p p {
                font-size: 12px !important;
            }



            .student_list_thead {
                display: none !important;
            }

            .dataTables_length,
            .dataTables_info {
                display: none !important;
            }

            #basic-datatables_paginate {
                display: none !important;
            }

        }


        @media only screen and (min-width: 768px) {
            .students_thead {
                background: #294282 !important;

            }
        }

        .make_status_false_btn{
            position: absolute;
            top: -10px;
            right: -15px;
            z-index: 10;
        }
    </style>
</head>

<body>

    <!-- Sidebar -->
    {% include "sidebar.html" %}
    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
            DASHBOARD / STUDENTS
        </div>
    </div>

    <div class="container-fluid mt-3">

        <div class="row">
            <div class="col-md-12 mb-3">
                <a href="/students/create/" class="btn btn-success btn-lg">Create Student</a>
            </div>
        </div>
        <div class="row">
            <!-- Messages Section -->
            {% if messages %}
            <div id="messageContainer">
                {% for message in messages %}
                {% if message.tags == 'success' %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {% elif message.tags == 'error' %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {% elif message.tags == 'warning' %}
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            {% elif message.tags == 'info' %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {% else %}
                                <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                    {% endif %}
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}


                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-dark text-white">
                                        <h4 class="card-title mb-0">Student Information</h4>
                                    </div>
                                    <div class="card-body ">
                                        <!-- Table -->
                                        <div class="table-responsive">
                                            <table class="table table-bordered" id="basic-datatables">
                                                <thead class="thead students_thead text-white">
                                                    <tr>
                                                        <th>Student Id</th>
                                                        <th>Course</th>
                                                        <th>Name</th>
                                                        <th>Mobile Number</th>
                                                        <th>Email</th>
                                                        <th>Due Date</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="table-body">
                                                    {% for data in std %}
                                                    <tr
                                                        class="{% if data.student.color == 'blue' %}bg-light-blue{% elif data.student.color == 'green' %}bg-light-green{% elif data.student.color == 'red' %}bg-light-red{% elif data.student.color == 'grey' %}bg-light-grey{% else %}bg-white{% endif %}">
                                                        <td class="text-center">
                                                            <a href="/students/{{ data.student.slug }}/">
                                                                {{data.student.unique_id }}</a>
                                                        </td>
                                                        <td>{{ data.student.course }}</td>
                                                        <td>{{ data.student.name }}</td>
                                                        <td>
                                                            {{ data.student.mobile }}
                                                            <i class="fas fa-copy"
                                                                onclick="copyToClipboard('{{ data.student.mobile }}')"
                                                                title="Copy"></i>
                                                            <a href="tel:{{ data.student.mobile }}">
                                                                <i class="fas fa-phone" title="Call"></i>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            {{ data.student.email }}
                                                            <i class="fas fa-copy"
                                                                onclick="copyToClipboard('{{ data.student.email }}')"
                                                                title="Copy"></i>
                                                            <a href="mailto:{{ data.student.email }}">
                                                                <i class="fas fa-envelope" title="Send Email"></i>
                                                            </a>
                                                        </td>

                                                        <td>{{ data.due_date }}</td>

                                                        <td class="text-center">
                                                            <a href="/students/{{ data.student.slug }}/send-email/"
                                                                class="btn btn-outline-primary btn-sm"><i
                                                                    class="fa-solid fa-envelope"></i></a>
                                                            <a href="/students/{{ data.student.slug }}/send-sms/"
                                                                class="btn btn-outline-success btn-sm"><i
                                                                    class="fa-solid fa-message"></i></a>
                                                            <a href="/your-action-url"
                                                                class="btn btn-outline-danger btn-sm" title="Disable">
                                                                <i class="fas fa-ban"></i>
                                                            </a>
                                                        </td>
                                                    </tr>

                                                    {% endfor %}
                                                </tbody>
                                            </table>

                                        </div>
                                    </div>
                                    <div class="container d-md-none">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group d-flex align-items-center">
                                                    <label for="entries" class="mr-2 mb-0">Show entries:</label>
                                                    <select id="entries" class="form-control form-control-sm w-auto">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group d-flex align-items-center">
                                                    <label for="search" class="mr-2 mb-0">Search:</label>
                                                    <input type="text" id="search"
                                                        class="form-control form-control-sm w-auto">
                                                </div>
                                            </div>
                                        </div>

                                        <div id="studentCards" class="student-cards ">
                                            {% for data in std %}
                                            <div
                                            class="card mb-3 student-card status-{{ data.student.color|default:'normal' }}">
                                            <button id="make_status_false_btn" onclick="approve_function('{{data.id}}')" class="btn btn-sm text-danger rounded-circle make_status_false_btn">
                                                <i class="fas fa-times" style="font-size: 28px;"></i>
                                            </button>
                                                <div class="card-header d-flex align-items-center">
                                                    <div class="w-50">
                                                        <h5 class="card-title mb-0 text-white">{{ data.student.name }} </h5>
                                                    </div>
                                                    <div class="w-50">
                                                        <a href="/students/{{ data.student.slug }}/"
                                                            class="btn btn-primary btn-sm float-right">Details</a>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12+">
                                                            <p class="card-text">
                                                                <strong>Course:</strong> {{ data.student.course }}
                                                            </p>
                                                            <p class="card-text">
                                                                <strong>Mobile:</strong> {{ data.student.mobile }}
                                                                <i class="fas fa-copy ml-2"
                                                                    onclick="copyToClipboard('{{ data.student.mobile }}')"
                                                                    title="Copy"></i>
                                                                <a href="tel:{{ data.student.mobile }}" class="ml-2">
                                                                    <i class="fas fa-phone-alt" title="Call"></i>
                                                                </a>
                                                            </p>
                                                            <div class="card-text d-flex align-items-center">
                                                                <div class="w-60">
                                                                    <strong>Email:</strong> {{ data.student.email }}
                                                                </div>
                                                                <div class="w-40">
                                                                    <i class="fas fa-copy ml-2"
                                                                        onclick="copyToClipboard('{{ data.student.email }}')"
                                                                        title="Copy"></i>
                                                                    <a href="mailto:{{ data.student.email }}"
                                                                        class="ml-2">
                                                                        <i class="fas fa-envelope-open"
                                                                            title="Send Email"></i>
                                                                    </a>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12">
                                                <nav aria-label="Page navigation">
                                                    <ul class="pagination justify-content-center">
                                                        <!-- Pagination will be dynamically generated -->
                                                    </ul>
                                                </nav>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                        <!-- footer -->
                        <div class="footer">
                            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy"
                                style="filter: invert(0.5) brightness(0);">
                            <!-- <p>Developed with passion by Librainian</p> -->
                        </div>
                    </div>


                    <!-- JavaScript dependencies -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
                    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
                    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

                    <script>
                        // Show success message and hide after 10 seconds
                        document.addEventListener('DOMContentLoaded', function () {
                            var alerts = document.querySelectorAll('.alert');
                            alerts.forEach(function (alert) {
                                setTimeout(function () {
                                    alert.style.transition = 'opacity 1s';
                                    alert.style.opacity = '0';
                                    setTimeout(function () {
                                        alert.style.display = 'none';
                                    }, 1000);
                                }, 10000);
                            });
                        });
                    </script>
                    <script>
                        document.addEventListener("DOMContentLoaded", function () {
                            const searchInput = document.getElementById('search');
                            const entriesSelect = document.getElementById('entries');
                            const studentCards = document.getElementById('studentCards');
                            const pagination = document.querySelector('.pagination');

                            let students = Array.from(studentCards.children);
                            let currentPage = 1;
                            let entriesPerPage = parseInt(entriesSelect.value);

                            // Function to filter the students based on search input
                            function filterCards() {
                                const searchTerm = searchInput.value.toLowerCase();
                                const filteredStudents = students.filter(student => {
                                    return student.querySelector('.card-title').textContent.toLowerCase().includes(searchTerm);
                                });
                                paginate(filteredStudents);
                            }

                            // Function to paginate the filtered students
                            function paginate(filteredStudents) {
                                const totalStudents = filteredStudents.length;
                                const totalPages = Math.ceil(totalStudents / entriesPerPage);
                                const start = (currentPage - 1) * entriesPerPage;
                                const end = start + entriesPerPage;
                                const paginatedStudents = filteredStudents.slice(start, end);

                                studentCards.innerHTML = '';
                                paginatedStudents.forEach(student => studentCards.appendChild(student));

                                pagination.innerHTML = '';

                                // Previous button
                                const prevPage = document.createElement('li');
                                prevPage.classList.add('page-item');
                                if (currentPage === 1) prevPage.classList.add('disabled');
                                const prevLink = document.createElement('a');
                                prevLink.classList.add('page-link');
                                prevLink.textContent = 'Previous';
                                prevLink.addEventListener('click', function (e) {
                                    e.preventDefault();
                                    if (currentPage > 1) {
                                        currentPage--;
                                        paginate(filteredStudents); // Re-paginate with updated currentPage
                                    }
                                });
                                prevPage.appendChild(prevLink);
                                pagination.appendChild(prevPage);

                                // Ellipsis and page range calculation
                                const range = 2; // Number of pages to show before and after current page
                                let startPage = Math.max(1, currentPage - range);
                                let endPage = Math.min(totalPages, currentPage + range);

                                // Show first page and ellipsis if there are skipped pages
                                if (currentPage - range > 1) {
                                    pagination.appendChild(createPageLink(1)); // Show the first page
                                    pagination.appendChild(createEllipsis());  // Add ellipsis
                                }

                                // Show page numbers around the current page
                                for (let i = startPage; i <= endPage; i++) {
                                    pagination.appendChild(createPageLink(i, i === currentPage)); // Create page links
                                }

                                // Show ellipsis and last page if there are skipped pages
                                if (currentPage + range < totalPages) {
                                    pagination.appendChild(createEllipsis()); // Add ellipsis
                                    pagination.appendChild(createPageLink(totalPages)); // Show the last page
                                }

                                // Next button
                                const nextPage = document.createElement('li');
                                nextPage.classList.add('page-item');
                                if (currentPage === totalPages) nextPage.classList.add('disabled');
                                const nextLink = document.createElement('a');
                                nextLink.classList.add('page-link');
                                nextLink.textContent = 'Next';
                                nextLink.addEventListener('click', function (e) {
                                    e.preventDefault();
                                    if (currentPage < totalPages) {
                                        currentPage++;
                                        paginate(filteredStudents); // Re-paginate with updated currentPage
                                    }
                                });
                                nextPage.appendChild(nextLink);
                                pagination.appendChild(nextPage);
                            }

                            // Helper function to create a page link
                            function createPageLink(page, isActive = false) {
                                const pageItem = document.createElement('li');
                                pageItem.classList.add('page-item');
                                if (isActive) pageItem.classList.add('active');
                                const pageLink = document.createElement('a');
                                pageLink.classList.add('page-link');
                                pageLink.textContent = page;
                                pageLink.addEventListener('click', function (e) {
                                    e.preventDefault();
                                    currentPage = page;
                                    paginate(filteredStudents); // Call paginate function with updated currentPage
                                });
                                pageItem.appendChild(pageLink);
                                return pageItem;
                            }

                            // Helper function to create ellipsis
                            function createEllipsis() {
                                const ellipsisItem = document.createElement('li');
                                ellipsisItem.classList.add('page-item');
                                const ellipsisLink = document.createElement('span');
                                ellipsisLink.classList.add('page-link');
                                ellipsisLink.textContent = '...';
                                ellipsisItem.appendChild(ellipsisLink);
                                return ellipsisItem;
                            }

                            // Event listener for changing the number of entries per page
                            entriesSelect.addEventListener('change', function () {
                                entriesPerPage = parseInt(this.value);
                                currentPage = 1;  // Reset to the first page
                                filterCards();  // Re-filter and re-paginate
                            });

                            // Event listener for search input
                            searchInput.addEventListener('keyup', filterCards);

                            // Initialize the filtering and pagination
                            filterCards();
                        });

                    </script>

                    <script>
                        $(document).ready(function () {
                            $("#basic-datatables").DataTable({
                                "pageLength": 5, // Default number of entries per page
                                "lengthMenu": [5, 10, 25, 50, 75, 100], // Options for the number of entries to show
                                "paging": true // Enable pagination
                            });
                        });
                    </script>
                    <script>
                        // Show success message and hide after 5 seconds
                        $(document).ready(function () {
                            $('.alert').fadeIn().delay(5000).fadeOut();
                        });
                    </script>

                     
                    <script>
                        // Get the menu icon and submenu elements
                        const menuIcon = document.getElementById('menu-icon');
                        const submenu = document.getElementById('submenu');

                        // Add click event listener to the menu icon
                        menuIcon.addEventListener('click', function () {
                            submenu.classList.toggle('show');
                        });
                    </script>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            var menuIcon = document.getElementById('menu-icon');
                            var submenu = document.getElementById('submenu');

                            // Toggle submenu visibility on menu icon click
                            menuIcon.addEventListener('click', function () {
                                if (submenu.style.display === 'block') {
                                    submenu.style.display = 'none';
                                } else {
                                    submenu.style.display = 'block';
                                }
                            });
                        });

                    </script>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            var footerSearch = document.getElementById('footer-search');
                            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

                            footerSearch.addEventListener('click', function () {
                                exampleModal.show();
                            });
                        });
                    </script>
                    <script>
                        function updateDateTime() {
                            const now = new Date();
                            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
                            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

                            document.getElementById('date').textContent = 'Date: ' + date;
                            document.getElementById('time').textContent = 'Time: ' + time;
                        }

                        // Update the date and time on page load
                        updateDateTime();

                        // Update the date and time every minute
                        setInterval(updateDateTime, 60000);
                    </script>
                    <script>
                        function copyToClipboard(text) {
                            navigator.clipboard.writeText(text).then(() => {
                                alert('Copied to clipboard: ' + text);
                            }).catch(err => {
                                console.error('Error copying to clipboard: ', err);
                            });
                        }
                    </script>
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
                        rel="stylesheet">

                    <link rel="stylesheet"
                        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

    <script>
        function approve_function(id) {

            var url = "/students/student_active/" + id;
            fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },

            }).then(response => {
                if (response.ok) {
                    location.reload();
                }
            });
        }
    </script>
</body>

</html>
