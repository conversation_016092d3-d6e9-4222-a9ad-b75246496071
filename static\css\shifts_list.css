body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
    background-color: #f5f0f0;
}

.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.seats_container {
    margin: 0.5rem;
    padding: 0.5rem 0.7rem;
    border-radius: 1rem;
    background: #dfecea;
}

.seats_container a {
   color: #28345a !important;
}

.card {
    border-radius: 1rem !important;
}

hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.profile-img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    /* margin-bottom: 15px; */
    border-radius: 50%;
    margin-left: 10px;
}



.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

@media only screen and (max-width: 767px) {
    .small_p p{
    font-size: 12px !important;
}
}
.btn-success {
    background-color: white;
    color: #000000;
}

.btn-success:hover {
    background-color: green;
    color: white;
}

.btn-danger {
    background-color: white;
    color: rgb(0, 0, 0);
}

.btn-danger:hover {
    background-color: rgb(230, 17, 17);
    color: rgb(255, 255, 255);
}

.btn-link {
    color: #000000;
    padding-left: 2px;
    margin-bottom: -12px;
}

.btn-link:hover {
    color: #000000;
    text-decoration: none;
}

.submenu a {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #333;
    /* Example text color */
}

.submenu a:hover {
    background-color: #f0f0f0;
    /* Example hover background color */
}


.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;

}

/* 
.footer p {
    font-size: 12px;
    padding-top: 5px;
} */

@media (max-width:768px) {
    .footer {
        margin-bottom: 100px;
    }

    .footer img {
        width: 60%;
    }
    .shift-detail{
        margin-top: 20px;
    }

}

#notificationBtn {
    display: none;
}
.icon-text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-text-wrapper i {
    font-size: 24px;
    /* Adjust the icon size as needed */
    margin-bottom: 5px;
    /* Space between icon and text */
}

.dashboard-text {
    font-size: 12px;
    /* Match the font size to the icon size */
    line-height: 24px;
    /* Set the line-height to match the icon's height */
}


@media only screen and (min-width: 767px) {
    .footer {
        margin-left: 9rem;
    }
}