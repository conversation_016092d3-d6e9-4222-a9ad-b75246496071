from django.db import models
from django.contrib.auth.models import User
from libraryCommander.models import *


# Admin Model


class Manager_param(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    library_commander = models.Foreign<PERSON>ey(
        LibraryCommander_param, on_delete=models.CASCADE
    )
    manager_phone_num = models.BigIntegerField(default=9999999999)
    manager_role = models.CharField(max_length=50, default="Manager")
    manager_address = models.TextField()
    is_manager = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} | {self.manager_role}"
