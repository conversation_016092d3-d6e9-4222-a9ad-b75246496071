from django.db import models
from django.contrib.auth.models import User


# Admin Model


class LibraryCommander_param(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    librarycommander_phone_num = models.BigIntegerField(default=9999999999)
    librarycommander_role = models.CharField(max_length=50, default="librarycommander")
    librarycommander_address = models.TextField()
    is_admin = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} | {self.librarycommander_role}"


class Contact_User(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.CharField(max_length=250)
    message = models.TextField()

    def __str__(self):
        return f"{self.name} {self.email}"


class Subscribe_Email(models.Model):
    email = models.EmailField()

    def __str__(self):
        return self.email
