<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Report Generator</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Custom styling */
        body {
            background-color: #f4f6f9;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 1200px;
            padding-top: 50px;
        }

        .report-form {
            background: linear-gradient(to right, #ffffff, #f8f9fa);
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 40px;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .btn-generate {
            background: linear-gradient(to right, #007bff, #0056b3);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-generate:hover {
            transform: scale(1.05);
            background: linear-gradient(to right, #0056b3, #003f7f);
        }

        .report-table {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .report-table thead {
            background-color: #007bff;
            color: white;
        }

        .totals-card {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .list-group-item {
            background-color: transparent;
            border-color: rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .report-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="text-center text-primary mb-4">Generate Transaction Report</h2>
        {%if role == 'sublibrarian'%}
            <form method="POST" action="{% url 'transaction-report-sublib' %}" class="report-form bg-white p-5 rounded shadow-lg border border-light">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label text-secondary">Start Date (Up to last 3 months):</label>
                        <input type="date" name="start_date" id="start_date" min="{{three_months_ago|date:'Y-m-d'}}"
                            value="{{ start_date|date:'Y-m-d' }}" 
                            class="form-control form-control-lg" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label text-secondary">End Date:</label>
                        <input type="date" name="end_date" id="end_date" 
                            value="{{ end_date|date:'Y-m-d' }}" 
                            class="form-control form-control-lg" required>
                    </div>
                </div>

                <button type="submit" name="generate_report" value="true" 
                        class="btn btn-generate btn-primary btn-lg w-100 mt-3 shadow-md">
                    Generate Report
                </button>
            </form>
        {%else%}
        <form method="POST" action="{% url 'transaction-report' %}" class="report-form bg-white p-5 rounded shadow-lg border border-light">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="start_date" class="form-label text-secondary">Start Date (Up to last 3 months):</label>
                    <input type="date" name="start_date" id="start_date" min="{{three_months_ago|date:'Y-m-d'}}"
                           value="{{ start_date|date:'Y-m-d' }}" 
                           class="form-control form-control-lg" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="end_date" class="form-label text-secondary">End Date:</label>
                    <input type="date" name="end_date" id="end_date" 
                           value="{{ end_date|date:'Y-m-d' }}" 
                           class="form-control form-control-lg" required>
                </div>
            </div>

            <button type="submit" name="generate_report" value="true" 
                    class="btn btn-generate btn-primary btn-lg w-100 mt-3 shadow-md">
                Generate Report
            </button>
        </form>
        {%endif%}
        

        {% if generated_report %}
            <div class="mt-5">
                <h3 class="text-center text-primary mb-4">
                    Transaction Report 
                    <small class="text-muted d-block mt-2">
                        {{ start_date }} to {{ end_date }}
                    </small>
                </h3>

                <div class="table-responsive">
                    <table id="transactionTable" class="table report-table table-hover table-bordered table-sm shadow-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Sales (Cash)</th>
                                <th>Sales (Online)</th>
                                <th>Total Daily Collection</th>
                                <th>Closing Balance (Online)</th>
                                <th>Closing Balance (Cash)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date }}</td>
                                <td>{{ transaction.sales_cash }}</td>
                                <td>{{ transaction.sales_online }}</td>
                                <td>{{ transaction.total_daily_collection }}</td>
                                <td>{{ transaction.closing_balance_online }}</td>
                                <td>{{ transaction.closing_balance_cash }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-danger fw-bold">
                                    No transactions found for the selected date range.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Download Button -->
                <div class="d-flex justify-content-center mt-4">
                    <button class="btn btn-success btn-lg w-50" id="downloadBtn">
                        Download Report (Excel)
                    </button>
                </div>

                <!-- Calculated Totals Card -->
                <div class="card totals-card mt-4 p-4 shadow-sm">
                    <h5 class="card-title text-primary mb-3">Calculated Totals</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Total Cash Sales
                                    <span class="badge bg-primary rounded-pill">{{ total_cash }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Total Online Sales
                                    <span class="badge bg-primary rounded-pill">{{ total_online }}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Total Collection
                                    <span class="badge bg-success rounded-pill">{{ total_daily_collection }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Closing Balance (Online)
                                    <span class="badge bg-info rounded-pill">{{ closing_balance_online }}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Closing Balance (Cash)
                                    <span class="badge bg-warning rounded-pill">{{ closing_balance_cash }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Include xlsx library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>

    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            // Get the table data
            var wb = XLSX.utils.table_to_book(document.getElementById('transactionTable'), { sheet: "Transaction Report" });

            // Add totals as a new sheet
            var totalsData = [
                ["Calculated Totals", "", ""],
                ["Total Cash Sales", "{{ total_cash }}"],
                ["Total Online Sales", "{{ total_online }}"],
                ["Total Daily Collection", "{{ total_daily_collection }}"],
                ["Closing Balance (Online)", "{{ closing_balance_online }}"],
                ["Closing Balance (Cash)", "{{ closing_balance_cash }}"]
            ];
            
            var ws_totals = XLSX.utils.aoa_to_sheet(totalsData);
            XLSX.utils.book_append_sheet(wb, ws_totals, "Calculated Totals");

            // Write the workbook to a binary string
            var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

            // Create a Blob object from the binary string
            var blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });

            // Create a download link for the Blob
            var link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'transaction_report.xlsx';
            link.click();
        });

        // Function to convert binary string to array buffer
        function s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
            return buf;
        }
    </script>
</body>
</html>
